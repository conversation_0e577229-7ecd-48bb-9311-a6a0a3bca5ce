{"author": "https://nnxscan.io", "background": {"page": "background.html", "persistent": true}, "browser_action": {"default_icon": {"16": "images/icon-16.png", "19": "images/icon-19.png", "32": "images/icon-32.png", "38": "images/icon-38.png", "64": "images/icon-64.png", "128": "images/icon-128.png", "512": "images/icon-512.png"}, "default_title": "NeoNix Wallet", "default_popup": "popup-init.html"}, "commands": {"_execute_browser_action": {"suggested_key": {"default": "Alt+Shift+M", "windows": "Alt+Shift+M", "mac": "Alt+Shift+M", "chromeos": "Alt+Shift+M", "linux": "Alt+Shift+M"}}}, "content_scripts": [{"matches": ["file://*/*", "http://*/*", "https://*/*"], "js": ["scripts/disable-console.js", "scripts/lockdown-install.js", "scripts/lockdown-run.js", "scripts/lockdown-more.js", "scripts/contentscript.js", "scripts/inpage.js"], "run_at": "document_start", "all_frames": true}, {"matches": ["*://connect.trezor.io/*/popup.html*"], "js": ["vendor/trezor/content-script.js"]}], "default_locale": "en", "description": "__MSG_appDescription__", "icons": {"16": "images/icon-16.png", "19": "images/icon-19.png", "32": "images/icon-32.png", "38": "images/icon-38.png", "48": "images/icon-48.png", "64": "images/icon-64.png", "128": "images/icon-128.png", "512": "images/icon-512.png"}, "manifest_version": 2, "name": "__MSG_appName__", "optional_permissions": ["clipboardRead"], "permissions": ["storage", "unlimitedStorage", "clipboardWrite", "http://*/*", "https://*/*", "ws://*/*", "wss://*/*", "activeTab", "webRequest", "webRequestBlocking", "*://*.eth/", "notifications"], "short_name": "__MSG_appName__"}