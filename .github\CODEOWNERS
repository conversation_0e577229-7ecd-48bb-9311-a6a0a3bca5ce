# Lines starting with '#' are comments.

# GUIDELINES:
# Each line is a file pattern followed by one or more owners.
# Owners bear a responsibility to the organization and the users of this
# application. Repository administrators have the ability to merge pull
# requests that have not yet received the requisite reviews as outlined
# in this file. Do not force merge any PR without confidence that it
# follows all policies or without full understanding of the impact of
# those changes on build, release and publishing outcomes.

# LavaMoat policy changes can highlight security risks. Teams are encouraged to
# audit these changes on their own, and leave their analysis in a comment.
# These codeowners will review this analysis, and review the policy changes in
# further detail if warranted.
lavamoat/                            @MetaMask/extension-devs @MetaMask/policy-reviewers @MetaMask/supply-chain

# The offscreen.ts script file that is included in the offscreen document html
# file is responsible, at present, for loading the snaps execution environment
# for MV3. Any changes to this file should require at least one member of the
# snaps development team to review and approve the changes.
offscreen/scripts/offscreen.ts       @MetaMask/snaps-devs


# The .circleci/ folder instructs Circle CI on the process by which it
# should test, build and publish releases of our application. Due to the
# impact that changes to the files contained within this folder may have
# on our releases, only those with the knowledge and responsibility to
# publish libraries under the MetaMask name may approve those changes.
# Note to reviewers: We employ the use of CircleCI "Orbs", which are
# remotely hosted sections of CircleCI configuration and scripts, to
# empower our CI steps. ANY addition of orbs to our CircleCI config
# should be brought to the attention of engineering leadership for
# discussion
.circleci/                           @MetaMask/extension-security-team

# The privacy-snapshot.json file includes a list of all hosts that the
# extension communicates with during the E2E test suite runs. It is not a
# complete list of all hosts that the extension communicates with until the E2E
# test suite has full coverage. Anytime the privacy-snapshot file changes,
# extra scrutiny should be applied to the pull request to confirm that it does
# not broaden the number of hosts the extension communicates with without also
# providing a path for users to avoid that communication. MetaMask strives to
# make all such communication opt IN versus opt OUT.
privacy-snapshot.json                @MetaMask/extension-privacy-reviewers


# A machine-generated file that tracks circular dependencies in the codebase.
# It is updated using yarn circular-deps:update
development/circular-deps.jsonc      @MetaMask/extension-security-team @HowardBraham @dbrans

# The CODEOWNERS file constitutes an agreement amongst organization
# admins and maintainers to restrict approval capabilities to a subset
# of contributors. Modifications to this file result in a modification of
# that agreement and can only be approved by those with the knowledge
# and responsibility to publish libraries under the MetaMask name.
.github/CODEOWNERS                   @MetaMask/extension-security-team

# For now, restricting approvals inside the .devcontainer folder to devs
# who were involved with the Codespaces project.
.devcontainer/                       @MetaMask/extension-security-team @HowardBraham

# Design System to own code for the component-library folder
# Slack handle: @metamask-design-system-team | Slack channel: #metamask-design-system
ui/components/component-library      @MetaMask/design-system-engineers

# The Notifications team is responsible for code related to notifications

# Controllers
**/controllers/metamask-notifications/**            @MetaMask/notifications
**/controllers/push-platform-notifications/**       @MetaMask/notifications

# UI
**/metamask-notifications/**                        @MetaMask/notifications
**/multichain/notification*/**                      @MetaMask/notifications
**/pages/notification*/**                           @MetaMask/notifications
**/utils/notification.util.ts                       @MetaMask/notifications

# Identity team is responsible for authentication and backup and sync inside the Extension.
# Slack handle: @identity_team | Slack channel: #metamask-identity
**/identity/**                                          @MetaMask/identity

# Accounts team is responsible for code related with snap management accounts
# Slack handle: @accounts-team-devs | Slack channel: #metamask-accounts-team

app/scripts/lib/snap-keyring                        @MetaMask/accounts-engineers

# Multichain Accounts
# This feature is owned by the multichain account tiger team
# Slack handle: @multichain-accounts-devs | Slack channel: #metamask-bip44-team
**/multichain-accounts/**                           @MetaMask/accounts-engineers

# Swaps-Bridge team to own code for the swaps folder
ui/pages/swaps                                        @MetaMask/swaps-engineers
app/scripts/controllers/swaps                         @MetaMask/swaps-engineers

# Swaps-Bridge team to own code for the bridge folder
**/bridge/**                                          @MetaMask/swaps-engineers
**/bridge-status/**                                   @MetaMask/swaps-engineers

# Ramps team to own code for the ramps folder
**/ramps/**                                           @MetaMask/ramp

# Snaps
**/snaps/**                          @MetaMask/snaps-devs
shared/constants/permissions.ts      @MetaMask/snaps-devs
ui/helpers/utils/permission.js       @MetaMask/snaps-devs
app/scripts/constants/snaps.ts       @MetaMask/snaps-devs

# Co-owned by Confirmations and Snaps
ui/components/app/metamask-template-renderer @MetaMask/confirmations @MetaMask/snaps-devs

# Wallet UX
ui/components/app/whats-new-popup     @MetaMask/wallet-ux
ui/css                                @MetaMask/wallet-ux
ui/pages/home                         @MetaMask/wallet-ux
ui/components/multichain/             @MetaMask/wallet-ux

# Co-owned by accounts and wallet-ux
ui/components/multichain/multi-srp/              @MetaMask/accounts-engineers @MetaMask/wallet-ux
ui/components/multichain/account-picker/         @MetaMask/accounts-engineers @MetaMask/wallet-ux
ui/components/multichain/account-details/        @MetaMask/accounts-engineers @MetaMask/wallet-ux
ui/components/multichain/account-overview/       @MetaMask/accounts-engineers @MetaMask/wallet-ux
ui/components/multichain/account-list-item/      @MetaMask/accounts-engineers @MetaMask/wallet-ux
ui/components/multichain/account-list-menu/      @MetaMask/accounts-engineers @MetaMask/wallet-ux
ui/components/multichain/account-list-item-menu/ @MetaMask/accounts-engineers @MetaMask/wallet-ux

# Web3Auth / Onboarding
ui/pages/onboarding-flow              @MetaMask/web3auth

# Confirmations team to own code for confirmations on UI.
app/scripts/controller-init/confirmations                         @MetaMask/confirmations
app/scripts/lib/ppom                                              @MetaMask/confirmations
app/scripts/lib/signature                                         @MetaMask/confirmations
app/scripts/lib/transaction/decode                                @MetaMask/confirmations
app/scripts/lib/transaction/metrics.*                             @MetaMask/confirmations
app/scripts/lib/transaction/util.*                                @MetaMask/confirmations
ui/pages/confirmations                                            @MetaMask/confirmations
ui/components/multichain/pages/send                               @MetaMask/confirmations

# Assets
ui/components/app/detected-token                   @MetaMask/metamask-assets
ui/components/app/import-token                        @MetaMask/metamask-assets
ui/components/app/assets                              @MetaMask/metamask-assets
ui/components/ui/deprecated-networks                  @MetaMask/metamask-assets
ui/components/ui/nft-collection-image                 @MetaMask/metamask-assets

# Extension Platform
.yarnrc.yml                                           @MetaMask/extension-platform
test/e2e/mock-e2e-allowlist.js                        @MetaMask/extension-platform
