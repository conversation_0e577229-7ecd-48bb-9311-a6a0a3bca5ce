<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1080" height="1080" viewBox="0 0 1080 1080" xml:space="preserve">
<desc>Created with Fabric.js 5.2.4</desc>
<defs>
</defs>
<g transform="matrix(1 0 0 1 540 540)" id="33ec6045-f7e3-4c6b-900c-12953fc82693"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;" vector-effect="non-scaling-stroke"  x="-540" y="-540" rx="0" ry="0" width="1080" height="1080" />
</g>
<g transform="matrix(1 0 0 1 540 540)" id="59500206-5266-4427-96c8-bb866038bb1e"  >
</g>
<g transform="matrix(5.15 0 0 5.15 540 540)"  >
<g style="" vector-effect="non-scaling-stroke"   >
		<g transform="matrix(1 0 0 1 0 0)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,144,255); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-99.69, -99.67)" d="M 99.7000822 199.345425 C 44.6715107 199.55611100000002 -0.2484892599999995 154.53135 0 99.20182580000001 C 0.25055836 44.504682900000006 44.9515107 -0.3296027899999956 100.353416 1.4210854715202004e-14 C 154.962939 0.3256353000000142 199.814368 45.018968600000015 199.387701 100.51420700000001 C 198.968654 154.969445 154.570558 199.52182600000003 99.7000822 199.34542500000003" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 3.39 3.72)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-103.08, -103.39)" d="M 112.082939 91.6172544 C 120.776273 92.8458258 129.273416 94.043921 138.033416 95.2820163 C 138.770558 92.6458258 139.397225 90.39820669999999 140.11532 87.82868289999999 C 131.105796 86.49534959999998 122.507701 85.22296859999999 113.27532 83.85725439999999 C 116.38008199999999 72.87058769999999 119.06198699999999 62.36582579999999 122.41627299999999 52.076301999999984 C 123.52103499999998 48.689635299999985 125.989606 45.51058769999999 128.50579599999998 42.880111499999984 C 131.80674899999997 39.43249239999999 136.37436799999998 40.19630199999998 139.17055799999997 43.984873399999984 C 140.34198699999996 45.571540099999986 141.44484399999996 47.24963529999998 142.84865399999998 48.60773049999998 C 144.641035 50.33915909999998 147.28293899999997 51.181063899999984 149.08293899999998 49.49534959999998 C 150.265796 48.388682899999985 150.22770099999997 45.78487339999998 150.33817699999997 43.826778199999985 C 150.38198699999998 43.04011149999999 149.45436799999996 42.114397199999985 148.81246299999998 41.388682899999985 C 145.686749 37.84963529999999 141.463892 36.59630199999999 137.004844 36.409635299999984 C 127.05627299999999 35.99249239999998 119.91722499999999 41.06487339999998 114.311511 48.73154009999998 C 108.75722499999999 56.329635299999985 106.321035 65.26296859999998 103.669606 74.09154009999997 C 102.89627300000001 76.66868289999998 102.233416 79.28011149999998 101.456273 82.10868289999998 C 91.7705584 80.70677819999997 82.4791298 79.36201629999998 72.917225 77.98106389999998 C 72.5781774 80.56201629999998 72.2619869 82.98296859999998 71.9248441 85.55439719999998 C 81.3915107 87.01154009999998 90.4486536 88.40392099999998 99.8086536 89.84392099999998 C 98.5553203 95.12392099999998 97.3877012 100.05153999999997 96.18770119999999 105.10677799999998 C 87.0067488 103.82296899999997 78.30198689999999 102.60963499999998 69.5381774 101.38296899999997 C 69.1667488 104.21344499999998 68.88674879999999 106.35249199999997 68.55532029999999 108.87058799999997 C 77.35722499999999 110.21534999999997 85.84293929999998 111.51058799999997 94.3857965 112.81534999999997 C 94.3857965 113.77915899999996 94.4905584 114.33915899999997 94.3686536 114.84773099999997 C 91.6429393 126.46487299999997 89.10770120000001 138.13344499999997 86.0505584 149.66106399999995 C 84.589606 155.15820699999995 82.2391298 160.46487299999995 77.669606 164.32773099999994 C 73.9077012 167.50677799999994 69.7743679 166.97534999999993 66.9457965 163.03058799999994 C 65.6677012 161.25344499999994 64.6010345 159.14296899999994 62.9134155 157.89725399999995 C 61.6810345 156.98487299999994 58.9381774 156.43630199999996 58.0334155 157.16773099999995 C 56.6924631 158.25344499999994 55.721034499999995 160.65534999999994 55.8353203 162.41915899999995 C 55.9324631 163.98296899999994 57.437225 165.81153999999995 58.818177399999996 166.87820699999995 C 63.471510699999996 170.46106399999994 68.9477012 170.98106399999995 74.4962726 169.88963499999994 C 83.78960599999999 168.05915899999994 90.3438917 162.26106399999995 94.4067488 154.06106399999993 C 97.5991298 147.61534999999992 99.98960600000001 140.73153999999994 102.248654 133.88011099999994 C 104.294368 127.67630199999994 105.682939 121.25534999999994 107.416273 114.71058799999994 C 116.543892 116.03439699999994 125.49055800000001 117.33153999999995 134.621987 118.65534999999994 C 135.376273 116.04963499999994 136.05627299999998 113.69153999999995 136.789606 111.15249199999994 C 127.23531999999999 109.74868299999994 118.11912999999998 108.40582599999993 108.924844 107.05344499999994 C 110.02960599999999 101.65725399999994 111.012463 96.85154009999994 112.082939 91.61725439999994" stroke-linecap="round" />
</g>
</g>
</g>
</svg>