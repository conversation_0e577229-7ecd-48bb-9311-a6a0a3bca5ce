name: Crowdin Action

on:
  push:
    branches:
      - main
  schedule:
    - cron: '0 */12 * * *'

jobs:
  synchronize-with-crowdin:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          # Use PAT to ensure that the commit later can trigger status check workflows
          token: ${{ secrets.METAMASKBOT_CROWDIN_TOKEN }}

      - name: crowdin action
        uses: crowdin/github-action@a3160b9e5a9e00739392c23da5e580c6cabe526d
        with:
          upload_translations: false
          download_translations: true
          github_user_name: metamaskbot
          github_user_email: <EMAIL>
        env:
          GITHUB_ACTOR: metamaskbot
          GITHUB_TOKEN: ${{ secrets.METAMASKBOT_CROWDIN_TOKEN }}
          CROWDIN_PROJECT_ID: ${{ secrets.CROWDIN_PROJECT_ID }}
          CROWDIN_PERSONAL_TOKEN: ${{ secrets.CROWDIN_PERSONAL_TOKEN }}
