diff --git a/index.js b/index.js
index 806efc9587acf7e91a37e22c6a26c477f2ead538..d9fed7067dc595e622ed26d66ff8628d6df478b0 100644
--- a/index.js
+++ b/index.js
@@ -1,11 +1,11 @@
-const crypto = require('crypto');
-const EventEmitter = require('events').EventEmitter;
-const BN = require('bn.js');
-const SDK = require('gridplus-sdk');
-const EthTx = require('@ethereumjs/tx');
+const crypto = require("crypto");
+const EventEmitter = require("events").EventEmitter;
+const BN = require("bn.js");
+const SDK = require("gridplus-sdk");
+const EthTx = require("@ethereumjs/tx");
 const { addHexPrefix } = require("@ethereumjs/util");
-const rlp = require('rlp');
-const keyringType = 'Lattice Hardware';
+const rlp = require("rlp");
+const keyringType = "Lattice Hardware";
 const HARDENED_OFFSET = 0x80000000;
 const PER_PAGE = 5;
 const CLOSE_CODE = -1000;
@@ -14,8 +14,8 @@ const SDK_TIMEOUT = 120000;
 const CONNECT_TIMEOUT = 20000;
 
 class LatticeKeyring extends EventEmitter {
-  constructor (opts={}) {
-    super()
+  constructor(opts = {}) {
+    super();
     this.type = keyringType;
     this._resetDefaults();
     this.deserialize(opts);
@@ -24,27 +24,19 @@ class LatticeKeyring extends EventEmitter {
   //-------------------------------------------------------------------
   // Keyring API (per `https://github.com/MetaMask/eth-simple-keyring`)
   //-------------------------------------------------------------------
-  async deserialize (opts = {}) {
-    if (opts.hdPath)
-      this.hdPath = opts.hdPath;
-    if (opts.creds)
-      this.creds = opts.creds;
-    if (opts.accounts)
-      this.accounts = opts.accounts;
-    if (opts.accountIndices)
-      this.accountIndices = opts.accountIndices;
-    if (opts.accountOpts)
-      this.accountOpts = opts.accountOpts;
-    if (opts.walletUID)
-      this.walletUID = opts.walletUID;
-    if (opts.name)  // Legacy; use is deprecated and appName is more descriptive
+  async deserialize(opts = {}) {
+    if (opts.hdPath) this.hdPath = opts.hdPath;
+    if (opts.creds) this.creds = opts.creds;
+    if (opts.accounts) this.accounts = opts.accounts;
+    if (opts.accountIndices) this.accountIndices = opts.accountIndices;
+    if (opts.accountOpts) this.accountOpts = opts.accountOpts;
+    if (opts.walletUID) this.walletUID = opts.walletUID;
+    if (opts.name)
+      // Legacy; use is deprecated and appName is more descriptive
       this.appName = opts.name;
-    if (opts.appName)
-      this.appName = opts.appName;
-    if (opts.network)
-      this.network = opts.network;
-    if (opts.page)
-      this.page = opts.page;
+    if (opts.appName) this.appName = opts.appName;
+    if (opts.network) this.network = opts.network;
+    if (opts.page) this.page = opts.page;
     return;
   }
 
@@ -60,7 +52,7 @@ class LatticeKeyring extends EventEmitter {
       accountOpts: this.accountOpts,
       walletUID: this.walletUID,
       appName: this.appName,
-      name: this.name,  // Legacy; use is deprecated
+      name: this.name, // Legacy; use is deprecated
       network: this.network,
       page: this.page,
       hdPath: this.hdPath,
@@ -69,7 +61,7 @@ class LatticeKeyring extends EventEmitter {
 
   // Deterimine if we have a connection to the Lattice and an existing wallet UID
   // against which to make requests.
-  isUnlocked () {
+  isUnlocked() {
     return !!this._getCurrentWalletUID() && !!this.sdkSession;
   }
 
@@ -80,7 +72,7 @@ class LatticeKeyring extends EventEmitter {
   // We avoid passing `bypassOnStateData=true` for other calls on `unlock` to avoid
   // possible edge cases related to this new functionality (it's probably fine - just
   // being cautious). In the future we may remove `bypassOnStateData` entirely.
-  async unlock (bypassOnStateData = false) {
+  async unlock(bypassOnStateData = false) {
     if (this.isUnlocked()) {
       return "Unlocked";
     }
@@ -101,42 +93,44 @@ class LatticeKeyring extends EventEmitter {
   }
 
   // Add addresses to the local store and return the full result
-  async addAccounts(n=1) {
+  async addAccounts(n = 1) {
     if (n <= 0) {
       // Avoid non-positive numbers.
-      throw new Error(
-        'Number of accounts to add must be a positive number.'
-      );
+      throw new Error("Number of accounts to add must be a positive number.");
     }
     // Normal behavior: establish the connection and fetch addresses.
-    await this.unlock()
+    await this.unlock();
     const addrs = await this._fetchAddresses(n, this.unlockedAccount);
     const walletUID = this._getCurrentWalletUID();
     if (!walletUID) {
       // We should not add accounts that do not have wallet UIDs.
       // Something went wrong and needs to be retried.
       await this._connect();
-      throw new Error('No active wallet found in Lattice. Please retry.');
+      throw new Error("No active wallet found in Lattice. Please retry.");
     }
     // Add these indices
     addrs.forEach((addr, i) => {
       let alreadySaved = false;
       for (let j = 0; j < this.accounts.length; j++) {
-        if ((this.accounts[j] === addr) &&
-            (this.accountOpts[j].walletUID === walletUID) &&
-            (this.accountOpts[j].hdPath === this.hdPath))
+        if (
+          this.accounts[j] === addr &&
+          this.accountOpts[j].walletUID === walletUID &&
+          this.accountOpts[j].hdPath === this.hdPath
+        )
           alreadySaved = true;
       }
       if (!alreadySaved) {
         this.accounts.push(addr);
-        this.accountIndices.push(this.unlockedAccount+i);
+        this.accountIndices.push(this.unlockedAccount + i);
         this.accountOpts.push({
           walletUID,
           hdPath: this.hdPath,
-        })
+        });
       }
-    })
-    return this.accounts;
+    });
+
+    /** PATCH: Patch the account array to return the new account */
+    return [this.accounts[this.accounts.length - 1]];
   }
 
   // Return the local store of addresses. This gets called when the extension unlocks.
@@ -144,7 +138,7 @@ class LatticeKeyring extends EventEmitter {
     return this.accounts ? [...this.accounts] : [];
   }
 
-  async signTransaction (address, tx) {
+  async signTransaction(address, tx) {
     let signedTx, v;
     // We will be adding a signature to hydration data for a new
     // transaction object since the sig data is not mutable.
@@ -161,7 +155,7 @@ class LatticeKeyring extends EventEmitter {
     // Lattice firmware v0.11.0 implemented EIP1559 and EIP2930
     // We should throw an error if we cannot support this.
     if (fwVersion.major === 0 && fwVersion.minor <= 11) {
-      throw new Error('Please update Lattice firmware.');
+      throw new Error("Please update Lattice firmware.");
     }
     // Build the signing request
     if (fwVersion.major > 0 || fwVersion.minor >= 15) {
@@ -169,17 +163,23 @@ class LatticeKeyring extends EventEmitter {
       const data = {
         // Legacy transactions return tx params. Newer transactions
         // return the raw, serialized transaction
-        payload:  tx._type ?
-                  tx.getMessageToSign(false) :
-                  rlp.encode(tx.getMessageToSign(false)),
+        payload: tx._type
+          ? tx.getMessageToSign(false)
+          : rlp.encode(tx.getMessageToSign(false)),
         curveType: SDK.Constants.SIGNING.CURVES.SECP256K1,
         hashType: SDK.Constants.SIGNING.HASHES.KECCAK256,
         encodingType: SDK.Constants.SIGNING.ENCODINGS.EVM,
         signerPath,
       };
-      const supportsDecoderRecursion = fwVersion.major > 0 || fwVersion.minor >=16;
+      const supportsDecoderRecursion =
+        fwVersion.major > 0 || fwVersion.minor >= 16;
       // Check if we can decode the calldata
-      const { def } = await SDK.Utils.fetchCalldataDecoder(tx.data, tx.to, chainId, supportsDecoderRecursion);
+      const { def } = await SDK.Utils.fetchCalldataDecoder(
+        tx.data,
+        tx.to,
+        chainId,
+        supportsDecoderRecursion
+      );
       if (def) {
         data.decoder = def;
       }
@@ -190,11 +190,11 @@ class LatticeKeyring extends EventEmitter {
       const data = getLegacyTxReq(tx);
       data.chainId = chainId;
       data.signerPath = signerPath;
-      signedTx = await this.sdkSession.sign({ currency: 'ETH', data });
+      signedTx = await this.sdkSession.sign({ currency: "ETH", data });
     }
     // Ensure we got a signature back
     if (!signedTx.sig || !signedTx.sig.r || !signedTx.sig.s) {
-      throw new Error('No signature returned.');
+      throw new Error("No signature returned.");
     }
     // Construct the `v` signature param
     if (signedTx.sig.v === undefined) {
@@ -202,12 +202,12 @@ class LatticeKeyring extends EventEmitter {
       v = SDK.Utils.getV(tx, signedTx);
     } else {
       // Legacy signatures have `v` in the response
-      v = signedTx.sig.v.length === 0 ? '0' : signedTx.sig.v.toString('hex')
+      v = signedTx.sig.v.length === 0 ? "0" : signedTx.sig.v.toString("hex");
     }
 
     // Pack the signature into the return object
-    txToReturn.r = addHexPrefix(signedTx.sig.r.toString('hex'));
-    txToReturn.s = addHexPrefix(signedTx.sig.s.toString('hex'));
+    txToReturn.r = addHexPrefix(signedTx.sig.r.toString("hex"));
+    txToReturn.s = addHexPrefix(signedTx.sig.s.toString("hex"));
     txToReturn.v = addHexPrefix(v);
 
     // Make sure the active wallet is correct to avoid returning
@@ -215,29 +215,33 @@ class LatticeKeyring extends EventEmitter {
     const foundIdx = await this._accountIdxInCurrentWallet(address);
     if (foundIdx === null) {
       throw new Error(
-        'Wrong account. Please change your Lattice wallet or ' +
-        'switch to an account on your current active wallet.'
+        "Wrong account. Please change your Lattice wallet or " +
+          "switch to an account on your current active wallet."
       );
     }
     return EthTx.TransactionFactory.fromTxData(txToReturn, {
-      common: tx.common, freeze: Object.isFrozen(tx)
-    })
+      common: tx.common,
+      freeze: Object.isFrozen(tx),
+    });
   }
 
   async signPersonalMessage(address, msg) {
-    return this.signMessage(address, { payload: msg, protocol: 'signPersonal' });
+    return this.signMessage(address, {
+      payload: msg,
+      protocol: "signPersonal",
+    });
   }
 
   async signTypedData(address, msg, opts) {
-    if (opts.version && (opts.version !== 'V4' && opts.version !== 'V3')) {
+    if (opts.version && opts.version !== "V4" && opts.version !== "V3") {
       throw new Error(
         `Only signTypedData V3 and V4 messages (EIP712) are supported. Got version ${opts.version}`
       );
     }
-    return this.signMessage(address, { payload: msg, protocol: 'eip712' })
+    return this.signMessage(address, { payload: msg, protocol: "eip712" });
   }
 
-  async signMessage (address, msg) {
+  async signMessage(address, msg) {
     const accountIdx = await this._findSignerIdx(address);
     let { payload, protocol } = msg;
     // If the message is not an object we assume it is a legacy signPersonal request
@@ -274,8 +278,8 @@ class LatticeKeyring extends EventEmitter {
     const foundIdx = await this._accountIdxInCurrentWallet(address);
     if (foundIdx === null) {
       throw new Error(
-        'Wrong account. Please change your Lattice wallet or ' +
-        'switch to an account on your current active wallet.'
+        "Wrong account. Please change your Lattice wallet or " +
+          "switch to an account on your current active wallet."
       );
     }
     // Return the sig string
@@ -283,7 +287,7 @@ class LatticeKeyring extends EventEmitter {
   }
 
   async exportAccount(address) {
-    throw new Error('exportAccount not supported by this device');
+    throw new Error("exportAccount not supported by this device");
   }
 
   removeAccount(address) {
@@ -294,7 +298,7 @@ class LatticeKeyring extends EventEmitter {
         this.accountOpts.splice(i, 1);
         return;
       }
-    })
+    });
   }
 
   async getFirstPage() {
@@ -302,19 +306,19 @@ class LatticeKeyring extends EventEmitter {
     return this._getPage(0);
   }
 
-  async getNextPage () {
+  async getNextPage() {
     return this._getPage(1);
   }
 
-  async getPreviousPage () {
+  async getPreviousPage() {
     return this._getPage(-1);
   }
 
-  setAccountToUnlock (index) {
-    this.unlockedAccount = parseInt(index, 10)
+  setAccountToUnlock(index) {
+    this.unlockedAccount = parseInt(index, 10);
   }
 
-  forgetDevice () {
+  forgetDevice() {
     this._resetDefaults();
   }
 
@@ -323,7 +327,7 @@ class LatticeKeyring extends EventEmitter {
   //-------------------------------------------------------------------
   // Find the account index of the requested address.
   // Note that this is the BIP39 path index, not the index in the address cache.
-  async _findSignerIdx (address) {
+  async _findSignerIdx(address) {
     // Take note if this was already unlocked
     const wasUnlocked = this.isUnlocked();
     // Unlock and get the wallet UID. We will bypass the reconnection
@@ -372,27 +376,26 @@ class LatticeKeyring extends EventEmitter {
     const addrs = await this.getAccounts();
     let accountIdx = -1;
     addrs.forEach((addr, i) => {
-      if (address.toLowerCase() === addr.toLowerCase())
-        accountIdx = i;
-    })
+      if (address.toLowerCase() === addr.toLowerCase()) accountIdx = i;
+    });
     if (accountIdx < 0) {
-      throw new Error('Signer not present');
+      throw new Error("Signer not present");
     }
     return accountIdx;
   }
 
-  _getHDPathIndices(hdPath, insertIdx=0) {
-    const path = hdPath.split('/').slice(1);
+  _getHDPathIndices(hdPath, insertIdx = 0) {
+    const path = hdPath.split("/").slice(1);
     const indices = [];
     let usedX = false;
     path.forEach((_idx) => {
-      const isHardened = (_idx[_idx.length - 1] === "'");
+      const isHardened = _idx[_idx.length - 1] === "'";
       let idx = isHardened ? HARDENED_OFFSET : 0;
       // If there is an `x` in the path string, we will use it to insert our
       // index. This is useful for e.g. Ledger Live path. Most paths have the
       // changing index as the last one, so having an `x` in the path isn't
       // usually necessary.
-      if (_idx.indexOf('x') > -1) {
+      if (_idx.indexOf("x") > -1) {
         idx += insertIdx;
         usedX = true;
       } else if (isHardened) {
@@ -401,7 +404,7 @@ class LatticeKeyring extends EventEmitter {
         idx += Number(_idx);
       }
       indices.push(idx);
-    })
+    });
     // If this path string does not include an `x`, we just append the index
     // to the end of the extracted set
     if (usedX === false) {
@@ -409,7 +412,7 @@ class LatticeKeyring extends EventEmitter {
     }
     // Sanity check -- Lattice firmware will throw an error for large paths
     if (indices.length > 5)
-      throw new Error('Only HD paths with up to 5 indices are allowed.')
+      throw new Error("Only HD paths with up to 5 indices are allowed.");
     return indices;
   }
 
@@ -443,13 +446,15 @@ class LatticeKeyring extends EventEmitter {
         // `window.open`. Instead, we need to use the `browser` API to open a tab.
         // We will surveille this tab to see if its URL parameters change, which
         // will indicate that the user has logged in.
-        const tab = await browser.tabs.create({url})
+        const tab = await browser.tabs.create({ url });
         return { firefox: tab };
       } else {
-        throw new Error('Unknown browser context. Cannot open Lattice connector.');
+        throw new Error(
+          "Unknown browser context. Cannot open Lattice connector."
+        );
       }
     } catch (err) {
-      throw new Error('Failed to open Lattice connector.');
+      throw new Error("Failed to open Lattice connector.");
     }
   }
 
@@ -461,28 +466,28 @@ class LatticeKeyring extends EventEmitter {
   _getCreds() {
     return new Promise((resolve, reject) => {
       // We only need to setup if we don't have a deviceID
-      if (this._hasCreds())
-        return resolve();
+      if (this._hasCreds()) return resolve();
       // If we are not aware of what Lattice we should be talking to,
       // we need to open a window that lets the user go through the
       // pairing or connection process.
-      const name = this.appName ? this.appName : 'Unknown'
-      const base = 'https://lattice.gridplus.io';
+      const name = this.appName ? this.appName : "Unknown";
+      const base = "https://lattice.gridplus.io";
       const url = `${base}?keyring=${name}&forceLogin=true`;
       let listenInterval;
 
       // PostMessage handler
       function receiveMessage(event) {
         // Ensure origin
-        if (event.origin !== base)
-          return;
+        if (event.origin !== base) return;
         try {
           // Stop the listener
           clearInterval(listenInterval);
           // Parse and return creds
           const creds = JSON.parse(event.data);
           if (!creds.deviceID || !creds.password)
-            return reject(new Error('Invalid credentials returned from Lattice.'));
+            return reject(
+              new Error("Invalid credentials returned from Lattice.")
+            );
           return resolve(creds);
         } catch (err) {
           return reject(err);
@@ -490,8 +495,7 @@ class LatticeKeyring extends EventEmitter {
       }
 
       // Open the tab
-      this._openConnectorTab(url)
-      .then((conn) => {
+      this._openConnectorTab(url).then((conn) => {
         if (conn.chromium) {
           // On a Chromium browser we can just listen for a window message
           window.addEventListener("message", receiveMessage, false);
@@ -499,7 +503,7 @@ class LatticeKeyring extends EventEmitter {
           listenInterval = setInterval(() => {
             if (conn.chromium.closed) {
               clearInterval(listenInterval);
-              return reject(new Error('Lattice connector closed.'));
+              return reject(new Error("Lattice connector closed."));
             }
           }, 500);
         } else if (conn.firefox) {
@@ -509,52 +513,56 @@ class LatticeKeyring extends EventEmitter {
           // the login info.
           // NOTE: This will only work if have `https://lattice.gridplus.io/*`
           // host permissions in your manifest file (and also `activeTab` permission)
-          const loginUrlParam = '&loginCache=';
+          const loginUrlParam = "&loginCache=";
           listenInterval = setInterval(() => {
-            this._findTabById(conn.firefox.id)
-            .then((tab) => {
+            this._findTabById(conn.firefox.id).then((tab) => {
               if (!tab || !tab.url) {
-                return reject(new Error('Lattice connector closed.'));
+                return reject(new Error("Lattice connector closed."));
               }
               // If the tab we opened contains a new URL param
               const paramLoc = tab.url.indexOf(loginUrlParam);
-              if (paramLoc < 0)
-                return;
+              if (paramLoc < 0) return;
               const dataLoc = paramLoc + loginUrlParam.length;
               // Stop this interval
               clearInterval(listenInterval);
               try {
                 // Parse the login data. It is a stringified JSON object
                 // encoded as a base64 string.
-                const _creds = Buffer.from(tab.url.slice(dataLoc), 'base64').toString();
+                const _creds = Buffer.from(
+                  tab.url.slice(dataLoc),
+                  "base64"
+                ).toString();
                 // Close the tab and return the credentials
-                browser.tabs.remove(tab.id)
-                .then(() => {
+                browser.tabs.remove(tab.id).then(() => {
                   const creds = JSON.parse(_creds);
                   if (!creds.deviceID || !creds.password)
-                    return reject(new Error('Invalid credentials returned from Lattice.'));
+                    return reject(
+                      new Error("Invalid credentials returned from Lattice.")
+                    );
                   return resolve(creds);
-                })
+                });
               } catch (err) {
-                return reject('Failed to get login data from Lattice. Please try again.')
+                return reject(
+                  "Failed to get login data from Lattice. Please try again."
+                );
               }
-            })
+            });
           }, 500);
         }
-      })
-    })
+      });
+    });
   }
 
   // [re]connect to the Lattice. This should be done frequently to ensure
   // the expected wallet UID is still the one active in the Lattice.
   // This will handle SafeCard insertion/removal events.
-  async _connect () {
+  async _connect() {
     try {
       // Attempt to connect with a Lattice using a shorter timeout. If
       // the device is unplugged it will time out and we don't need to wait
       // 2 minutes for that to happen.
       this.sdkSession.timeout = CONNECT_TIMEOUT;
-      return this.sdkSession.connect(this.creds.deviceID)
+      return this.sdkSession.connect(this.creds.deviceID);
     } finally {
       // Reset to normal timeout no matter what
       this.sdkSession.timeout = SDK_TIMEOUT;
@@ -565,9 +573,8 @@ class LatticeKeyring extends EventEmitter {
     if (this.isUnlocked()) {
       return;
     }
-    let url = 'https://signing.gridpl.us';
-    if (this.creds.endpoint)
-      url = this.creds.endpoint
+    let url = "https://signing.gridpl.us";
+    if (this.creds.endpoint) url = this.creds.endpoint;
     let setupData = {
       name: this.appName,
       baseUrl: url,
@@ -594,14 +601,14 @@ class LatticeKeyring extends EventEmitter {
     return !!setupData.stateData;
   }
 
-  async _fetchAddresses(n=1, i=0, recursedAddrs=[]) {
+  async _fetchAddresses(n = 1, i = 0, recursedAddrs = []) {
     if (!this.isUnlocked()) {
-      throw new Error('No connection to Lattice. Cannot fetch addresses.')
+      throw new Error("No connection to Lattice. Cannot fetch addresses.");
     }
     return this.__fetchAddresses(n, i);
   }
 
-  async __fetchAddresses(n=1, i=0, recursedAddrs=[]) {
+  async __fetchAddresses(n = 1, i = 0, recursedAddrs = []) {
     // Determine if we need to do a recursive call here. We prefer not to
     // because they will be much slower, but Ledger paths require it since
     // they are non-standard.
@@ -612,31 +619,34 @@ class LatticeKeyring extends EventEmitter {
 
     // Make the request to get the requested address
     const addrData = {
-      currency: 'ETH',
+      currency: "ETH",
       startPath: this._getHDPathIndices(this.hdPath, i),
       n: shouldRecurse ? 1 : n,
     };
     const addrs = await this.sdkSession.getAddresses(addrData);
     // Sanity check -- if this returned 0 addresses, handle the error
     if (addrs.length < 1) {
-      throw new Error('No addresses returned');
+      throw new Error("No addresses returned");
     }
     // Return the addresses we fetched *without* updating state
     if (shouldRecurse) {
-      return await this.__fetchAddresses(n-1, i+1, recursedAddrs.concat(addrs));
+      return await this.__fetchAddresses(
+        n - 1,
+        i + 1,
+        recursedAddrs.concat(addrs)
+      );
     }
     return addrs;
   }
 
-  async _getPage(increment=0) {
+  async _getPage(increment = 0) {
     try {
       this.page += increment;
-      if (this.page < 0)
-        this.page = 0;
+      if (this.page < 0) this.page = 0;
       const start = PER_PAGE * this.page;
       // Otherwise unlock the device and fetch more addresses
-      await this.unlock()
-      const addrs = await this._fetchAddresses(PER_PAGE, start)
+      await this.unlock();
+      const addrs = await this._fetchAddresses(PER_PAGE, start);
       const accounts = addrs.map((address, i) => {
         return {
           address,
@@ -655,37 +665,42 @@ class LatticeKeyring extends EventEmitter {
       try {
         const isPaired = await this._connect();
         if (!isPaired) {
-          throw new Error('NOT_PAIRED');
+          throw new Error("NOT_PAIRED");
         }
         const accounts = await this._getPage(0);
         return accounts;
       } catch (err) {
-        if (this.accounts.length === 0){
+        if (this.accounts.length === 0) {
           this.forgetDevice();
         }
         throw new Error(
-          'Failed to get accounts. Please forget the device and try again. ' +
-          'Make sure you do not have a locked SafeCard inserted.'
+          "Failed to get accounts. Please forget the device and try again. " +
+            "Make sure you do not have a locked SafeCard inserted."
         );
       }
     }
   }
 
   _hasCreds() {
-    return this.creds.deviceID !== null && this.creds.password !== null && this.appName;
+    return (
+      this.creds.deviceID !== null &&
+      this.creds.password !== null &&
+      this.appName
+    );
   }
 
   _genSessionKey() {
-    if (this.name && !this.appName) // Migrate from legacy param if needed
+    if (this.name && !this.appName)
+      // Migrate from legacy param if needed
       this.appName = this.name;
     if (!this._hasCreds())
-      throw new Error('No credentials -- cannot create session key!');
+      throw new Error("No credentials -- cannot create session key!");
     const buf = Buffer.concat([
       Buffer.from(this.creds.password),
       Buffer.from(this.creds.deviceID),
-      Buffer.from(this.appName)
-    ])
-    return crypto.createHash('sha256').update(buf).digest();
+      Buffer.from(this.appName),
+    ]);
+    return crypto.createHash("sha256").update(buf).digest();
   }
 
   // Determine if an HD path has a variable index internal to it.
@@ -693,10 +708,9 @@ class LatticeKeyring extends EventEmitter {
   // This is just a hacky helper to avoid having to recursively call for non-ledger
   // derivation paths. Ledger is SO ANNOYING TO SUPPORT.
   _hdPathHasInternalVarIdx() {
-    const path = this.hdPath.split('/').slice(1);
-    for (let i = 0; i < path.length -1; i++) {
-      if (path[i].indexOf('x') > -1)
-        return true;
+    const path = this.hdPath.split("/").slice(1);
+    for (let i = 0; i < path.length - 1; i++) {
+      if (path[i].indexOf("x") > -1) return true;
     }
     return false;
   }
@@ -709,15 +723,15 @@ class LatticeKeyring extends EventEmitter {
     if (!activeWallet || !activeWallet.uid) {
       return null;
     }
-    return activeWallet.uid.toString('hex');
+    return activeWallet.uid.toString("hex");
   }
 }
 
 // -----
 // HELPERS
 // -----
-function getTxChainId (tx) {
-  if (tx && tx.common && typeof tx.common.chainIdBN === 'function') {
+function getTxChainId(tx) {
+  if (tx && tx.common && typeof tx.common.chainIdBN === "function") {
     return tx.common.chainIdBN();
   } else if (tx && tx.chainId) {
     return new BN(tx.chainId);
@@ -727,50 +741,58 @@ function getTxChainId (tx) {
 
 // Legacy versions of Lattice firmware signed ETH transactions out of
 // a now deprecated pathway. The request data is built by this helper.
-function getLegacyTxReq (tx) {
+function getLegacyTxReq(tx) {
   let txData;
   try {
     txData = {
-      nonce: `0x${tx.nonce.toString('hex')}` || 0,
-      gasLimit: `0x${tx.gasLimit.toString('hex')}`,
-      to: !!tx.to ? tx.to.toString('hex') : null, // null for contract deployments
-      value: `0x${tx.value.toString('hex')}`,
-      data: tx.data.length === 0 ? null : `0x${tx.data.toString('hex')}`,
-    }
+      nonce: `0x${tx.nonce.toString("hex")}` || 0,
+      gasLimit: `0x${tx.gasLimit.toString("hex")}`,
+      to: !!tx.to ? tx.to.toString("hex") : null, // null for contract deployments
+      value: `0x${tx.value.toString("hex")}`,
+      data: tx.data.length === 0 ? null : `0x${tx.data.toString("hex")}`,
+    };
     switch (tx._type) {
       case 2: // eip1559
-        if ((tx.maxPriorityFeePerGas === null || tx.maxFeePerGas === null) ||
-            (tx.maxPriorityFeePerGas === undefined || tx.maxFeePerGas === undefined))
-          throw new Error('`maxPriorityFeePerGas` and `maxFeePerGas` must be included for EIP1559 transactions.');
-        txData.maxPriorityFeePerGas = `0x${tx.maxPriorityFeePerGas.toString('hex')}`;
-        txData.maxFeePerGas = `0x${tx.maxFeePerGas.toString('hex')}`;
+        if (
+          tx.maxPriorityFeePerGas === null ||
+          tx.maxFeePerGas === null ||
+          tx.maxPriorityFeePerGas === undefined ||
+          tx.maxFeePerGas === undefined
+        )
+          throw new Error(
+            "`maxPriorityFeePerGas` and `maxFeePerGas` must be included for EIP1559 transactions."
+          );
+        txData.maxPriorityFeePerGas = `0x${tx.maxPriorityFeePerGas.toString(
+          "hex"
+        )}`;
+        txData.maxFeePerGas = `0x${tx.maxFeePerGas.toString("hex")}`;
         txData.accessList = tx.accessList || [];
         txData.type = 2;
         break;
       case 1: // eip2930
         txData.accessList = tx.accessList || [];
-        txData.gasPrice = `0x${tx.gasPrice.toString('hex')}`;
+        txData.gasPrice = `0x${tx.gasPrice.toString("hex")}`;
         txData.type = 1;
         break;
       default: // legacy
-        txData.gasPrice = `0x${tx.gasPrice.toString('hex')}`;
+        txData.gasPrice = `0x${tx.gasPrice.toString("hex")}`;
         txData.type = null;
         break;
     }
   } catch (err) {
-    throw new Error(`Failed to build transaction.`)
+    throw new Error(`Failed to build transaction.`);
   }
   return txData;
 }
 
-async function httpRequest (url) {
+async function httpRequest(url) {
   const resp = await window.fetch(url);
   if (resp.ok) {
     return await resp.text();
   } else {
-    throw new Error('Failed to make request: ', resp.status);
+    throw new Error("Failed to make request: ", resp.status);
   }
 }
 
-LatticeKeyring.type = keyringType
+LatticeKeyring.type = keyringType;
 module.exports = LatticeKeyring;
