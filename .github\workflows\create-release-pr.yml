name: Create Release Pull Request

on:
  workflow_dispatch:
    inputs:
      base-branch:
        description: 'The base branch, tag, or SHA for git operations and the pull request.'
        required: true
      semver-version:
        description: 'A semantic version. eg: x.x.x'
        required: true
      previous-version-tag:
        description: 'Previous release version tag. eg: v7.7.0'
        required: true
jobs:
  create-release-pr:
    uses: MetaMask/github-tools/.github/workflows/create-release-pr.yml@d1ba843333b920fc9b0e1823fd519b7a64d07f5f
    with:
      platform: extension
      base-branch: ${{ inputs.base-branch }}
      semver-version: ${{ inputs.semver-version }}
      previous-version-tag: ${{ inputs.previous-version-tag }}
    secrets:
      # This token needs write permissions to metamask-extension & read permissions to metamask-planning
      github-token: ${{ secrets.PR_TOKEN }}
      google-application-creds-base64: ${{ secrets.GCP_RLS_SHEET_ACCOUNT_BASE64 }}
    permissions:
      contents: write
      pull-requests: write
