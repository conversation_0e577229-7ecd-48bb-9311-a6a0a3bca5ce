import { Meta } from '@storybook/addon-docs';

<Meta title="Getting Started / Introduction" />

# Introduction

Welcome to the MetaMask Browser Extension Storybook.

## Building locally and Contributing

This document is currently only specific to storybook best practices and component documentation guidelines. This may change in future but for now if you are looking to get a local build of MetaMask up and running or contribute to the extension codebase please read the MetaMask [README.md](https://github.com/MetaMask/metamask-extension)

## Documentation Guidelines

If you're here to improve our storybook documentation you're in the right place! Please head over to the [Documentation Guidelines](./?path=/story/getting-started-documentation-guidelines--page) for contributing to component documentation in storybook.
