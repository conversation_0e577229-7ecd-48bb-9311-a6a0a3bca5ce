name: Publish release

on:
  workflow_call:

jobs:
  publish-release:
    name: Publish release
    runs-on: ubuntu-latest
    env:
      CONTENTFUL_ACCESS_SPACE_ID: ${{ secrets.CONTENTFUL_ACCESS_SPACE_ID }}
      CONTENTFUL_ACCESS_TOKEN: ${{ secrets.CONTENTFUL_ACCESS_TOKEN }}
      ETHERSCAN_API_KEY: ${{ secrets.ETHERSCAN_API_KEY }}
      FIREBASE_API_KEY: ${{ secrets.FIREBASE_API_KEY }}
      FIREBASE_APP_ID: ${{ secrets.FIREBASE_APP_ID }}
      FIREBASE_AUTH_DOMAIN: ${{ secrets.FIREBASE_AUTH_DOMAIN }}
      FIREBASE_MEASUREMENT_ID: ${{ secrets.FIREBASE_MEASUREMENT_ID }}
      FIREBASE_MESSAGING_SENDER_ID: ${{ secrets.FIREBASE_MESSAGING_SENDER_ID }}
      FIREBASE_PROJECT_ID: ${{ secrets.FIREBASE_PROJECT_ID }}
      FIREBASE_STORAGE_BUCKET: ${{ secrets.FIREBASE_STORAGE_BUCKET }}
      INFURA_BETA_PROJECT_ID: ${{ secrets.INFURA_BETA_PROJECT_ID }}
      INFURA_FLASK_PROJECT_ID: ${{ secrets.INFURA_FLASK_PROJECT_ID }}
      INFURA_PROD_PROJECT_ID: ${{ secrets.INFURA_PROD_PROJECT_ID }}
      INFURA_PROJECT_ID: ${{ secrets.INFURA_PROJECT_ID }}
      PUBNUB_PUB_KEY: ${{ secrets.PUBNUB_PUB_KEY }}
      SEGMENT_BETA_WRITE_KEY: ${{ secrets.SEGMENT_BETA_WRITE_KEY }}
      SEGMENT_FLASK_WRITE_KEY: ${{ secrets.SEGMENT_FLASK_WRITE_KEY }}
      SEGMENT_PROD_WRITE_KEY: ${{ secrets.SEGMENT_PROD_WRITE_KEY }}
      SEGMENT_WRITE_KEY: ${{ secrets.SEGMENT_WRITE_KEY }}
      ANALYTICS_DATA_DELETION_SOURCE_ID: ${{ secrets.ANALYTICS_DATA_DELETION_SOURCE_ID }}
      ANALYTICS_DATA_DELETION_ENDPOINT: ${{ secrets.ANALYTICS_DATA_DELETION_ENDPOINT }}
      SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
      SENTRY_DSN: ${{ secrets.SENTRY_DSN }}
      SENTRY_DSN_DEV: ${{ secrets.SENTRY_DSN_DEV }}
      VAPID_KEY: ${{ secrets.VAPID_KEY }}
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      FIREFOX_BUNDLE_SCRIPT_TOKEN: ${{ secrets.FIREFOX_BUNDLE_SCRIPT_TOKEN }}
    steps:
      - name: Checkout and setup environment
        uses: MetaMask/action-checkout-and-setup@v1
        with:
          is-high-risk-environment: true
          skip-allow-scripts: true
          fetch-depth: 0
          yarn-custom-url: ${{ vars.YARN_URL }}

      - name: Download artifact 'build-dist-browserify'
        uses: actions/download-artifact@v4
        with:
          path: build-dist-browserify
          name: build-dist-browserify

      - name: Publish main release to Sentry
        run: yarn sentry:publish --dist-directory build-dist-browserify/dist

      - name: Download artifact 'build-dist-mv2-browserify'
        uses: actions/download-artifact@v4
        with:
          path: build-dist-mv2-browserify
          name: build-dist-mv2-browserify

      - name: Publish main MV2 release to Sentry
        run: yarn sentry:publish --dist mv2 --dist-directory build-dist-mv2-browserify/dist

      - name: Download artifact 'build-flask-browserify'
        uses: actions/download-artifact@v4
        with:
          path: build-flask-browserify
          name: build-flask-browserify

      - name: Publish Flask release to Sentry
        run: yarn sentry:publish --build-type flask --dist-directory build-flask-browserify/dist

      - name: Download artifact 'build-flask-mv2-browserify'
        uses: actions/download-artifact@v4
        with:
          path: build-flask-mv2-browserify
          name: build-flask-mv2-browserify

      - name: Publish Flask MV2 release to Sentry
        run: yarn sentry:publish --build-type flask --dist mv2 --dist-directory build-flask-mv2-browserify/dist

      - name: Create GitHub release
        run: .github/scripts/release-create-gh-release.sh

      - name: Push Firefox bundle script
        run: .github/scripts/push-firefox-bundle-script.sh
