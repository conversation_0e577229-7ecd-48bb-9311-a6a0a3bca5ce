import { CHAIN_IDS } from '../../../shared/constants/network';

export const SINGLE_CALL_BALANCES_ADDRESSES = {
  [CHAIN_IDS.MAINNET]: '0xb1f8e55c7f64d203c1400b9d8555d050f94adf39',
  [CHAIN_IDS.GOERLI]: '0x9788C4E93f9002a7ad8e72633b11E8d1ecd51f9b',
  // TODO(SEPOLIA) There is currently no balance call address for Sepolia
  // [CHAIN_IDS.SEPOLIA]: '',
  [CHAIN_IDS.BSC]: '0x2352c63A83f9Fd126af8676146721Fa00924d7e4',
  [CHAIN_IDS.OPTIMISM]: '0xB1c568e9C3E6bdaf755A60c7418C269eb11524FC',
  [CHAIN_IDS.POLYGON]: '0x2352c63A83f9Fd126af8676146721Fa00924d7e4',
  [CHAIN_IDS.AVALANCHE]: '0xD023D153a0DFa485130ECFdE2FAA7e612EF94818',
  [CHAIN_IDS.FANTOM]: '0x07f697424ABe762bB808c109860c04eA488ff92B',
  [CHAIN_IDS.ARBITRUM]: '0x151E24A486D7258dd7C33Fb67E4bB01919B7B32c',
  [CHAIN_IDS.BLAST]: '0xfd5730e96f9dffae40d99b77015bd42816280998',
  [CHAIN_IDS.LINEA_GOERLI]: '0x10dAd7Ca3921471f616db788D9300DC97Db01783',
  [CHAIN_IDS.LINEA_MAINNET]: '0xF62e6a41561b3650a69Bb03199C735e3E3328c0D',
  [CHAIN_IDS.AURORA]: '0x1286415D333855237f89Df27D388127181448538',
  [CHAIN_IDS.BASE]: '0x6AA75276052D96696134252587894ef5FFA520af',
  [CHAIN_IDS.ZKSYNC_ERA]: '0x458fEd3144680a5b8bcfaa0F9594aa19B4Ea2D34',
};
