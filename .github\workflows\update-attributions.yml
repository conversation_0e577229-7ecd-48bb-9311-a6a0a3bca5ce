name: Update Attributions

on:
  issue_comment:
    types: created

jobs:
  is-fork-pull-request:
    name: Determine whether this issue comment was on a pull request from a fork
    if: ${{ github.event.issue.pull_request && startsWith(github.event.comment.body, '@metamaskbot update-attributions') }}
    runs-on: ubuntu-latest
    outputs:
      IS_FORK: ${{ steps.is-fork.outputs.IS_FORK }}
    steps:
      - uses: actions/checkout@v4
      - name: Determine whether this PR is from a fork
        id: is-fork
        run: echo "IS_FORK=$(gh pr view --json isCrossRepository --jq '.isCrossRepository' "${PR_NUMBER}" )" >> "$GITHUB_OUTPUT"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          PR_NUMBER: ${{ github.event.issue.number }}

  react-to-comment:
    name: React to the comment
    runs-on: ubuntu-latest
    needs: is-fork-pull-request
    # Early exit if this is a fork, since later steps are skipped for forks
    if: ${{ needs.is-fork-pull-request.outputs.IS_FORK == 'false' }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: React to the comment
        run: |
          gh api \
            --method POST \
            -H "Accept: application/vnd.github+json" \
            -H "X-GitHub-Api-Version: 2022-11-28" \
            "/repos/${REPO}/issues/comments/${COMMENT_ID}/reactions" \
            -f content='+1'
        env:
          COMMENT_ID: ${{ github.event.comment.id }}
          GITHUB_TOKEN: ${{ secrets.LAVAMOAT_UPDATE_TOKEN }}
          REPO: ${{ github.repository }}

  prepare:
    name: Prepare dependencies
    runs-on: ubuntu-latest
    needs: is-fork-pull-request
    # Early exit if this is a fork, since later steps are skipped for forks
    if: ${{ needs.is-fork-pull-request.outputs.IS_FORK == 'false' }}
    outputs:
      COMMIT_SHA: ${{ steps.commit-sha.outputs.COMMIT_SHA }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Checkout pull request
        run: gh pr checkout "${PR_NUMBER}"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          PR_NUMBER: ${{ github.event.issue.number }}
      - name: Checkout and setup environment
        uses: MetaMask/action-checkout-and-setup@v1
        with:
          is-high-risk-environment: false
          skip-allow-scripts: true
          yarn-custom-url: ${{ vars.YARN_URL }}
      - name: Get commit SHA
        id: commit-sha
        run: echo "COMMIT_SHA=$(git rev-parse --short HEAD)" >> "$GITHUB_OUTPUT"

  update-attributions:
    name: Update Attributions
    runs-on: ubuntu-latest
    needs:
      - prepare
      - is-fork-pull-request
    # Early exit if this is a fork, since later steps are skipped for forks
    if: ${{ needs.is-fork-pull-request.outputs.IS_FORK == 'false' }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Checkout pull request
        run: gh pr checkout "${PR_NUMBER}"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          PR_NUMBER: ${{ github.event.issue.number }}
      - name: Checkout and setup environment
        uses: MetaMask/action-checkout-and-setup@v1
        with:
          is-high-risk-environment: false
          skip-allow-scripts: true
          yarn-custom-url: ${{ vars.YARN_URL }}
      - name: Generate Attributions
        run: yarn attributions:generate
      - name: Cache attributions file
        uses: actions/cache/save@v4
        with:
          path: attribution.txt
          key: cache-build-${{ needs.prepare.outputs.COMMIT_SHA }}

  commit-updated-attributions:
    name: Commit the updated Attributions
    runs-on: ubuntu-latest
    needs:
      - prepare
      - is-fork-pull-request
      - update-attributions
    if: ${{ needs.is-fork-pull-request.outputs.IS_FORK == 'false' }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          # Use PAT to ensure that the commit later can trigger status check workflows
          token: ${{ secrets.LAVAMOAT_UPDATE_TOKEN }}
      - name: Checkout pull request
        run: gh pr checkout "${PR_NUMBER}"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          PR_NUMBER: ${{ github.event.issue.number }}
      - name: Get commit SHA
        id: commit-sha
        run: echo "COMMIT_SHA=$(git rev-parse --short HEAD)" >> "$GITHUB_OUTPUT"
      - name: Restore attributions file
        uses: actions/cache/restore@v4
        with:
          path: attribution.txt
          key: cache-build-${{ needs.prepare.outputs.COMMIT_SHA }}
          fail-on-cache-miss: true
      - name: Check whether there are attributions changes
        id: attributions-changes
        run: |
          if git diff --exit-code
          then
            echo "HAS_CHANGES=false" >> "$GITHUB_OUTPUT"
          else
            echo "HAS_CHANGES=true" >> "$GITHUB_OUTPUT"
          fi
      - name: Commit the updated attributions
        if: steps.attributions-changes.outputs.HAS_CHANGES == 'true'
        run: |
          git config --global user.name 'MetaMask Bot'
          git config --global user.email '<EMAIL>'
          git commit -am "Update Attributions"
          git push
      - name: Post comment
        run: |
          if [[ $HAS_CHANGES == 'true' ]]
          then
            gh pr comment "${PR_NUMBER}" --body 'Attributions updated'
          else
            gh pr comment "${PR_NUMBER}" --body 'No attributions changes'
          fi
        env:
          HAS_CHANGES: ${{ steps.attributions-changes.outputs.HAS_CHANGES }}
          GITHUB_TOKEN: ${{ secrets.LAVAMOAT_UPDATE_TOKEN }}
          PR_NUMBER: ${{ github.event.issue.number }}

  check-status:
    name: Check whether the attributions update succeeded
    runs-on: ubuntu-latest
    needs:
      - commit-updated-attributions
      - is-fork-pull-request
    # Early exit if this is a fork, since later steps are skipped for forks
    if: ${{ needs.is-fork-pull-request.outputs.IS_FORK == 'false' }}
    outputs:
      PASSED: ${{ steps.set-output.outputs.PASSED }}
    steps:
      - name: Set PASSED output
        id: set-output
        run: echo "PASSED=true" >> "$GITHUB_OUTPUT"

  failure-comment:
    name: Comment about the attributions update failure
    if: ${{ !cancelled() && needs.is-fork-pull-request.outputs.IS_FORK == 'false' }}
    runs-on: ubuntu-latest
    needs:
      - is-fork-pull-request
      - check-status
    steps:
      - uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
      - name: Post comment if the update failed
        run: |
          passed="${{ needs.check-status.outputs.PASSED }}"
          if [[ $passed != "true" ]]; then
            gh pr comment "${PR_NUMBER}" --body "Attributions update failed. You can [review the logs or retry the attributions update here](${ACTION_RUN_URL})"
          fi
        env:
          GITHUB_TOKEN: ${{ secrets.LAVAMOAT_UPDATE_TOKEN }}
          PR_NUMBER: ${{ github.event.issue.number }}
          ACTION_RUN_URL: '${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}'
