<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1080" height="1080" viewBox="0 0 1080 1080" xml:space="preserve">
<desc>Created with Fabric.js 5.2.4</desc>
<defs>
</defs>
<g transform="matrix(1 0 0 1 540 540)" id="6d236122-bb3d-4e42-9c6b-8d2a0958998c"  >
</g>
<g transform="matrix(1 0 0 1 540 540)" id="e68d71f6-858d-496f-973e-8d82de5e6bfb"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;" vector-effect="non-scaling-stroke"  x="-540" y="-540" rx="0" ry="0" width="1080" height="1080" />
</g>
<g transform="matrix(12.89 0 0 12.89 539.9 539.9)" id="fc2f5ed2-9b97-4553-a90e-ba5317325e12"  >
<circle style="stroke: rgb(0,0,0); stroke-opacity: 0; stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(240,235,222); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  cx="0" cy="0" r="35" />
</g>
<g transform="matrix(1 0 0 1 540 540)"  >
<g style="" vector-effect="non-scaling-stroke"   >
		<g transform="matrix(1 0 0 1 -184.84 -28.38)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(19,54,41); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-215.27, -371.55)" d="M 131.685 352.096 C 131.319 328.91 138.859 306.291 153.063 287.962 L 298.861 433.76 C 280.484 447.873 257.897 455.402 234.727 455.138 C 207.433 455.025 181.289 444.133 161.989 424.833 C 142.69 405.534 131.797 379.39 131.685 352.096 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 185.45 -28.86)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(19,54,41); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-585.54, -371.07)" d="M 565.443 455.03 C 579.094 455.087 592.621 452.441 605.245 447.247 C 617.869 442.052 629.34 434.411 638.997 424.763 C 648.655 415.116 656.308 403.652 661.516 391.034 C 666.723 378.415 669.383 364.891 669.34 351.24 C 669.596 328.071 662.068 305.487 647.962 287.106 L 501.736 433.332 C 519.913 447.55 542.367 455.198 565.443 455.03 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 0 96.31)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(19,54,41); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-400.1, -496.24)" d="M 678.219 257.499 C 700.697 283.894 713.049 317.427 713.065 352.097 C 713.037 391.086 697.528 428.47 669.948 456.03 C 642.369 483.589 604.974 499.071 565.984 499.071 C 531.496 499.109 498.103 486.956 471.707 464.759 L 400.946 535.52 L 330.184 464.759 C 303.77 486.996 270.329 499.153 235.8 499.071 C 216.449 499.141 197.275 495.392 179.376 488.039 C 161.476 480.685 145.204 469.872 131.491 456.219 C 117.778 442.566 106.893 426.34 99.4623 408.473 C 92.0311 390.606 88.1987 371.448 88.1848 352.097 C 88.2124 317.587 100.356 284.183 122.497 257.713 L 89.4674 224.684 L 57.9353 192.616 C 19.9503 255.069 -0.0950739 326.78 0.000339029 399.877 C -0.0137127 452.416 10.3265 504.443 30.4292 552.985 C 50.532 601.526 80.0034 645.631 117.159 682.777 C 154.315 719.923 198.428 749.382 246.975 769.472 C 295.521 789.562 347.551 799.888 400.09 799.86 C 506.118 799.832 607.802 757.729 682.815 682.796 C 757.828 607.863 800.04 506.225 800.181 400.197 C 800.841 327.112 780.991 255.307 742.887 192.937 L 678.219 257.499 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 0 -162.9)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(19,54,41); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-400.09, -237.03)" d="M 689.873 123.993 C 652.563 84.7656 607.659 53.5429 557.896 32.2262 C 508.132 10.9095 454.551 -0.0550433 400.414 0.000207773 C 346.266 -0.0259924 292.677 10.9513 242.9 32.2658 C 193.123 53.5803 148.197 84.7874 110.848 123.993 C 101.121 134.682 91.6082 145.371 82.7363 156.808 L 400.093 474.058 L 717.45 156.488 C 709.071 144.991 699.854 134.13 689.873 123.993 L 689.873 123.993 Z M 400.414 399.984 L 154.566 154.136 C 186.709 121.65 225.008 95.8997 267.221 78.3905 C 309.434 60.8813 354.714 51.9648 400.414 52.1627 C 446.121 51.9109 491.418 60.8027 533.638 78.3151 C 575.859 95.8274 614.151 121.607 646.261 154.136 L 400.414 399.984 Z" stroke-linecap="round" />
</g>
</g>
</g>
<g transform="matrix(0 0 0 0 0 0)"  >
<g style=""   >
</g>
</g>
<g transform="matrix(0 0 0 0 0 0)"  >
<g style=""   >
</g>
</g>
<g transform="matrix(NaN NaN NaN NaN 0 0)"  >
<g style=""   >
</g>
</g>
<g transform="matrix(NaN NaN NaN NaN 0 0)"  >
<g style=""   >
</g>
</g>
</svg>