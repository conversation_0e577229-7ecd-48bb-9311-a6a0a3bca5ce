{"configurations": [{"type": "node", "request": "launch", "name": "Jest: current file", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["${fileBasenameNoExtension}", "--config", "--runInBand", "jest.config.js"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "windows": {"program": "${workspaceFolder}/node_modules/jest/bin/jest"}}, {"type": "node", "request": "launch", "name": "Jest Integration: current file", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["${fileBasenameNoExtension}", "--config", "jest.integration.config.js", "--testTimeout", "30000"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "windows": {"program": "${workspaceFolder}/node_modules/jest/bin/jest"}}, {"type": "node", "request": "launch", "name": "Test E2E: current file", "cwd": "${workspaceFolder}", "runtimeExecutable": "yarn", "runtimeArgs": ["test:e2e:single", "${relativeFile}", "--browser=${input:browserToUse}", "--debug"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}], "inputs": [{"type": "pickString", "id": "browserToUse", "description": "Which browser do you want to test with?", "options": ["chrome", "firefox", "all"], "default": "chrome"}], "version": "0.2.0"}