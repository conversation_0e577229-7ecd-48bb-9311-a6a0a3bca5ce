name: Validate Conventional Commit Title
on:
  pull_request:
    branches:
      - main
    types: [opened, edited, reopened, synchronize]

jobs:
  pr-title-linter:
    runs-on: ubuntu-latest
    steps:
      # this is a hash for amannn/action-semantic-pull-request@v5.4.0
      - uses: amannn/action-semantic-pull-request@e9fabac35e210fea40ca5b14c0da95a099eff26f
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
