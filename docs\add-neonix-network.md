# Adding NeoNix Network to MetaMask

This document provides instructions for adding the NeoNix Mainnet network to MetaMask wallet.

## Network Details

- **Network Name**: NeoNix Mainnet
- **Chain ID**: 989890 (0xf1ac2 in hex)
- **RPC URL**: https://rpc.nnxscan.io
- **Currency Symbol**: NNX
- **Block Explorer URL**: https://nnxscan.io

## Method 1: Using wallet_addEthereumChain (Programmatic)

If you're a developer integrating with NeoNix, you can use the following JavaScript code to prompt users to add the NeoNix network:

```javascript
async function addNeoNixNetwork() {
  try {
    await window.ethereum.request({
      method: 'wallet_addEthereumChain',
      params: [
        {
          chainId: '0xf1ac2', // 989890 in hex
          chainName: 'NeoNix Mainnet',
          nativeCurrency: {
            name: 'NeoNix',
            symbol: 'NNX',
            decimals: 18,
          },
          rpcUrls: ['https://rpc.nnxscan.io'],
          blockExplorerUrls: ['https://nnxscan.io'],
        },
      ],
    });
    console.log('NeoNix network added successfully');
  } catch (error) {
    console.error('Failed to add NeoNix network:', error);
  }
}

// Call the function
addNeoNixNetwork();
```

## Method 2: Manual Addition via MetaMask UI

1. Open MetaMask extension
2. Click on the network dropdown (usually shows "Ethereum Mainnet")
3. Click "Add a custom network" or "Add network"
4. Fill in the network details:
   - **Network Name**: NeoNix Mainnet
   - **New RPC URL**: https://rpc.nnxscan.io
   - **Chain ID**: 989890
   - **Currency Symbol**: NNX
   - **Block Explorer URL**: https://nnxscan.io
5. Click "Save"

## Method 3: Popular Networks List

NeoNix is included in the popular networks list in MetaMask. When adding a custom network:

1. Open MetaMask extension
2. Click on the network dropdown
3. Click "Add a custom network"
4. Look for "NeoNix" in the popular networks section
5. Click "Add" next to NeoNix

The network details will be automatically filled in for you.

## Verification

After adding the network, you should see:
- "NeoNix Mainnet" in your network dropdown
- NNX as the native currency symbol
- Ability to view transactions on nnxscan.io

## Troubleshooting

If you encounter issues:
1. Ensure the RPC URL is accessible: https://rpc.nnxscan.io
2. Verify the Chain ID is correct: 989890 (0xf1ac2)
3. Check that MetaMask is updated to the latest version
4. Try refreshing MetaMask or restarting your browser

## Additional Resources

- [NeoNix Block Explorer](https://nnxscan.io)
- [NeoNix RPC Endpoint](https://rpc.nnxscan.io)
- [MetaMask Documentation](https://docs.metamask.io/)
