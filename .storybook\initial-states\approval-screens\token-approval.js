export const currentNetworkTxListSample = {
  "id": 7900715443136469,
  "time": 1621395091737,
  "status": "unapproved",
  "chainId": "0x539",
  "loadingDefaults": false,
  "txParams": {
    "from": "0x90f79bf6eb2c4f870365e785982e1f101e93b906",
    "to": "0x057ef64e23666f000b34ae31332854acbd1c8544",
    "value": "0x0",
    "data": "0x095ea7b30000000000000000000000009bc5baf874d2da8d216ae9f137804184ee5afef40000000000000000000000000000000000000000000000000000000000011170",
    "gas": "0xea60",
    "gasPrice": "0x4a817c800"
  },
  "origin": "https://metamask.github.io",
  "type": "approve",
  "history": [
    {
      "id": 7900715443136469,
      "time": 1621395091737,
      "status": "unapproved",
      "chainId": "0x539",
      "loadingDefaults": true,
      "txParams": {
        "from": "0x90f79bf6eb2c4f870365e785982e1f101e93b906",
        "to": "0x057ef64e23666f000b34ae31332854acbd1c8544",
        "value": "0x0",
        "data": "0x095ea7b30000000000000000000000009bc5baf874d2da8d216ae9f137804184ee5afef40000000000000000000000000000000000000000000000000000000000011170",
        "gas": "0xea60",
        "gasPrice": "0x4a817c800"
      },
      "origin": "https://metamask.github.io",
      "type": "approve"
    },
    [
      {
        "op": "replace",
        "path": "/loadingDefaults",
        "value": false,
        "note": "Added new unapproved transaction.",
        "timestamp": 1621395091742
      }
    ]
  ]
}

export const subjectMetadata = {
  "https://metamask.github.io": {
    "origin": "https://metamask.github.io",
    "name": "E2E Test Dapp",
    "iconUrl": "https://metamask.github.io/test-dapp/metamask-fox.svg",
    "subjectType": "website"
  }
}
