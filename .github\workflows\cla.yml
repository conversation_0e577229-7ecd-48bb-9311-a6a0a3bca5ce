name: 'CLA Signature Bot'
on:
  issue_comment:
    types: [created]
  pull_request_target:
    types: [opened, closed, synchronize]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ !(contains(github.ref, 'refs/heads/main') || contains(github.ref, 'refs/heads/master')) }}

jobs:
  CLABot:
    if: github.event_name == 'pull_request_target' || contains(github.event.comment.html_url, '/pull/')
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
      contents: write
    steps:
      - name: 'CLA Signature Bot'
        uses: MetaMask/cla-signature-bot@v4.0.0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          path-to-signatures: 'cla.json'
          url-to-cladocument: 'https://metamask.io/cla'
          # This branch can't have protections, commits are made directly to the specified branch.
          branch: 'cla-signatures'
          allowlist: 'dependabot[bot],metamaskbot,crowdin-bot,runway-github[bot]'
          allow-organization-members: true
          blockchain-storage-flag: false
