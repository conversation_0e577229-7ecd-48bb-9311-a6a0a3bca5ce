{"QRHardwareInvalidTransactionTitle": {"message": "Error"}, "QRHardwareMismatchedSignId": {"message": "Incongruent transaction data. Please check the transaction details."}, "QRHardwarePubkeyAccountOutOfRange": {"message": "No more accounts. If you would like to access another account unlisted below, please reconnect your hardware wallet and select it."}, "QRHardwareScanInstructions": {"message": "Place the QR code in front of your camera. The screen is blurred, but it will not affect the reading."}, "QRHardwareSignRequestCancel": {"message": "Reject"}, "QRHardwareSignRequestDescription": {"message": "After you’ve signed with your wallet, click on 'Get Signature' to receive the signature"}, "QRHardwareSignRequestGetSignature": {"message": "Get signature"}, "QRHardwareSignRequestSubtitle": {"message": "Scan the QR code with your wallet"}, "QRHardwareSignRequestTitle": {"message": "Request signature"}, "QRHardwareUnknownQRCodeTitle": {"message": "Error"}, "QRHardwareUnknownWalletQRCode": {"message": "Invalid QR code. Please scan the sync QR code of the hardware wallet."}, "QRHardwareWalletImporterTitle": {"message": "Scan QR code"}, "QRHardwareWalletSteps1Description": {"message": "You can choose from a list of official QR-code supporting partners below."}, "QRHardwareWalletSteps1Title": {"message": "Connect your QR hardware wallet"}, "QRHardwareWalletSteps2Description": {"message": "<PERSON><PERSON>"}, "SrpListHideAccounts": {"message": "Hide $1 accounts", "description": "$1 is the number of accounts"}, "SrpListHideSingleAccount": {"message": "Hide 1 account"}, "SrpListShowAccounts": {"message": "Show $1 accounts", "description": "$1 is the number of accounts"}, "SrpListShowSingleAccount": {"message": "Show 1 account"}, "about": {"message": "About"}, "accept": {"message": "Accept"}, "acceptTermsOfUse": {"message": "I have read and agree to the $1", "description": "$1 is the `terms` message"}, "accessingYourCamera": {"message": "Accessing your camera..."}, "account": {"message": "Account"}, "accountActivity": {"message": "Account activity"}, "accountActivityText": {"message": "Select the accounts you want to be notified about:"}, "accountAlreadyExistsLogin": {"message": "Log in"}, "accountAlreadyExistsLoginDescription": {"message": "A wallet using “$1” already exists. Do you want to try logging in instead?", "description": "$1 is the account email"}, "accountAlreadyExistsTitle": {"message": "Wallet already exists"}, "accountDetails": {"message": "Account details"}, "accountIdenticon": {"message": "Account identicon"}, "accountIsntConnectedToastText": {"message": "$1 isn't connected to $2"}, "accountName": {"message": "Account name"}, "accountNameDuplicate": {"message": "This account name already exists", "description": "This is an error message shown when the user enters a new account name that matches an existing account name"}, "accountNameReserved": {"message": "This account name is reserved", "description": "This is an error message shown when the user enters a new account name that is reserved for future use"}, "accountNotFoundCreateOne": {"message": "Yes, create a new wallet"}, "accountNotFoundDescription": {"message": "We couldn’t find a wallet for “$1”. Do you want to create a new one with this login?", "description": "$1 is the account email"}, "accountNotFoundTitle": {"message": "Wallet not found"}, "accountOptions": {"message": "Account options"}, "accountPermissionToast": {"message": "Account permissions updated"}, "accountSelectionRequired": {"message": "You need to select an account!"}, "accountTypeNotSupported": {"message": "Account type not supported"}, "accounts": {"message": "Accounts"}, "accountsConnected": {"message": "Accounts connected"}, "accountsPermissionsTitle": {"message": "See your accounts and suggest transactions"}, "accountsSmallCase": {"message": "accounts"}, "active": {"message": "Active"}, "activity": {"message": "Activity"}, "activityLog": {"message": "Activity log"}, "add": {"message": "Add"}, "addACustomNetwork": {"message": "Add a custom network"}, "addANetwork": {"message": "Add a network"}, "addANickname": {"message": "Add a nickname"}, "addAUrl": {"message": "Add a URL"}, "addAccount": {"message": "Add account"}, "addAccountFromNetwork": {"message": "Add $1 account", "description": "$1 is the network name, e.g. Bitcoin or Solana"}, "addAccountToNeoNix": {"message": "Add account to NeoNix"}, "addAcquiredTokens": {"message": "Add the tokens you've acquired using NeoNix"}, "addAlias": {"message": "Add alias"}, "addBitcoinAccountLabel": {"message": "Bitcoin account"}, "addBlockExplorer": {"message": "Add a block explorer"}, "addBlockExplorerUrl": {"message": "Add a block explorer URL"}, "addContact": {"message": "Add contact"}, "addCustomNetwork": {"message": "Add custom network"}, "addEthereumChainWarningModalHeader": {"message": "Only add this RPC provider if you’re sure you can trust it. $1", "description": "$1 is addEthereumChainWarningModalHeaderPartTwo passed separately so that it can be bolded"}, "addEthereumChainWarningModalHeaderPartTwo": {"message": "Malicious providers may lie about the state of the blockchain and record your network activity."}, "addEthereumChainWarningModalListHeader": {"message": "It's important that your provider is reliable, as it has the power to:"}, "addEthereumChainWarningModalListPointOne": {"message": "See your accounts and IP address, and associate them together"}, "addEthereumChainWarningModalListPointThree": {"message": "Show account balances and other on-chain states"}, "addEthereumChainWarningModalListPointTwo": {"message": "Broadcast your transactions"}, "addEthereumChainWarningModalTitle": {"message": "You are adding a new RPC provider for Ethereum Mainnet"}, "addEthereumWatchOnlyAccount": {"message": "Watch an Ethereum account (Beta)"}, "addFriendsAndAddresses": {"message": "Add friends and addresses you trust"}, "addHardwareWalletLabel": {"message": "Hardware wallet"}, "addIPFSGateway": {"message": "Add your preferred IPFS gateway"}, "addImportAccount": {"message": "Add account or hardware wallet"}, "addMemo": {"message": "Add memo"}, "addNetwork": {"message": "Add network"}, "addNetworkConfirmationTitle": {"message": "Add $1", "description": "$1 represents network name"}, "addNewAccount": {"message": "Add a new Ethereum account"}, "addNewEthereumAccountLabel": {"message": "Ethereum account"}, "addNewSolanaAccountLabel": {"message": "Solana account"}, "addNft": {"message": "Add NFT"}, "addNfts": {"message": "Add NFTs"}, "addNonEvmAccount": {"message": "Add $1 account", "description": "$1 is the non EVM network where the account is going to be created, e.g. Bitcoin or Solana"}, "addNonEvmAccountFromNetworkPicker": {"message": "To enable the $1 network, you need to create a $2 account.", "description": "$1 is the non EVM network where the account is going to be created, e.g. Solana Mainnet or Solana Devnet. $2 is the account type, e.g. Bitcoin or Solana"}, "addRpcUrl": {"message": "Add RPC URL"}, "addSnapAccountToggle": {"message": "Enable \"Add account Snap (Beta)\""}, "addSnapAccountsDescription": {"message": "Turning on this feature will give you the option to add the new Beta account Snaps right from your account list. If you install an account Snap, remember that it is a third-party service."}, "addSuggestedNFTs": {"message": "<PERSON><PERSON> suggested NFTs"}, "addSuggestedTokens": {"message": "Add suggested tokens"}, "addToken": {"message": "Add token"}, "addTokenByContractAddress": {"message": "Can’t find a token? You can manually add any token by pasting its address. Token contract addresses can be found on $1", "description": "$1 is a blockchain explorer for a specific network, e.g. Etherscan for Ethereum"}, "addUrl": {"message": "Add URL"}, "addingAccount": {"message": "Adding account"}, "addingCustomNetwork": {"message": "Adding Network"}, "additionalNetworks": {"message": "Additional networks"}, "address": {"message": "Address"}, "addressCopied": {"message": "Address copied!"}, "addressMismatch": {"message": "Site address mismatch"}, "addressMismatchOriginal": {"message": "Current URL: $1", "description": "$1 replaced by origin URL in confirmation request"}, "addressMismatchPunycode": {"message": "Punycode version: $1", "description": "$1 replaced by punycode version of the URL in confirmation request"}, "advanced": {"message": "Advanced"}, "advancedBaseGasFeeToolTip": {"message": "When your transaction gets included in the block, any difference between your max base fee and the actual base fee will be refunded. Total amount is calculated as max base fee (in GWEI) * gas limit."}, "advancedDetailsDataDesc": {"message": "Data"}, "advancedDetailsHexDesc": {"message": "Hex"}, "advancedDetailsNonceDesc": {"message": "<PERSON><PERSON>"}, "advancedDetailsNonceTooltip": {"message": "This is the transaction number of an account. Nonce for the first transaction is 0 and it increases in sequential order."}, "advancedGasFeeDefaultOptIn": {"message": "Save these values as my default for the $1 network.", "description": "$1 is the current network name."}, "advancedGasFeeModalTitle": {"message": "Advanced gas fee"}, "advancedGasPriceTitle": {"message": "Gas price"}, "advancedPriorityFeeToolTip": {"message": "Priority fee (aka “miner tip”) goes directly to miners and incentivizes them to prioritize your transaction."}, "airDropPatternDescription": {"message": "The token's on-chain history reveals prior instances of suspicious airdrop activities."}, "airDropPatternTitle": {"message": "Airdrop Pattern"}, "airgapVault": {"message": "AirGap Vault"}, "alert": {"message": "<PERSON><PERSON>"}, "alertAccountTypeUpgradeMessage": {"message": "You're updating your account to a smart account. You'll keep the same account address while unlocking faster transactions and lower network fees. $1."}, "alertAccountTypeUpgradeTitle": {"message": "Account type"}, "alertActionBuyWithNativeCurrency": {"message": "Buy $1"}, "alertActionUpdateGas": {"message": "Update gas limit"}, "alertActionUpdateGasFee": {"message": "Update fee"}, "alertActionUpdateGasFeeLevel": {"message": "Update gas options"}, "alertContentMultipleApprovals": {"message": "You're giving someone else permission to withdraw your tokens, even though it's not necessary for this transaction."}, "alertDisableTooltip": {"message": "This can be changed in \"Settings > Alerts\""}, "alertMessageAddressMismatchWarning": {"message": "Attackers sometimes mimic sites by making small changes to the site address. Make sure you're interacting with the intended site before you continue."}, "alertMessageChangeInSimulationResults": {"message": "Estimated changes for this transaction have been updated. Review them closely before proceeding."}, "alertMessageFirstTimeInteraction": {"message": "You're interacting with this address for the first time. Make sure that it's correct before you continue."}, "alertMessageGasEstimateFailed": {"message": "We’re unable to provide an accurate fee and this estimate might be high. We suggest you to input a custom gas limit, but there’s a risk the transaction will still fail."}, "alertMessageGasFeeLow": {"message": "When choosing a low fee, expect slower transactions and longer wait times. For faster transactions, choose Market or Aggressive fee options."}, "alertMessageGasTooLow": {"message": "To continue with this transaction, you’ll need to increase the gas limit to 21000 or higher."}, "alertMessageInsufficientBalanceWithNativeCurrency": {"message": "You do not have enough $1 in your account to pay for network fees."}, "alertMessageNetworkBusy": {"message": "Gas prices are high and estimates are less accurate."}, "alertMessageNoGasPrice": {"message": "We can’t move forward with this transaction until you manually update the fee."}, "alertMessageSignInDomainMismatch": {"message": "The site making the request is not the site you’re signing into. This could be an attempt to steal your login credentials."}, "alertMessageSignInWrongAccount": {"message": "This site is asking you to sign in using the wrong account."}, "alertModalAcknowledge": {"message": "I have acknowledged the risk and still want to proceed"}, "alertModalDetails": {"message": "<PERSON><PERSON>"}, "alertModalReviewAllAlerts": {"message": "Review all alerts"}, "alertReasonChangeInSimulationResults": {"message": "Results have changed"}, "alertReasonFirstTimeInteraction": {"message": "1st interaction"}, "alertReasonGasEstimateFailed": {"message": "Inaccurate fee"}, "alertReasonGasFeeLow": {"message": "Slow speed"}, "alertReasonGasTooLow": {"message": "Low gas limit"}, "alertReasonInsufficientBalance": {"message": "Insufficient funds"}, "alertReasonMultipleApprovals": {"message": "Unnecessary permission"}, "alertReasonNetworkBusy": {"message": "Network is busy"}, "alertReasonNoGasPrice": {"message": "Fee estimate unavailable"}, "alertReasonPendingTransactions": {"message": "Pending transaction"}, "alertReasonSignIn": {"message": "Suspicious sign-in request"}, "alertReasonWrongAccount": {"message": "Wrong account"}, "alertSelectedAccountWarning": {"message": "This request is for a different account than the one selected in your wallet. To use another account, connect it to the site."}, "alerts": {"message": "<PERSON><PERSON><PERSON>"}, "all": {"message": "All"}, "allNetworks": {"message": "All networks"}, "allPermissions": {"message": "All permissions"}, "allTimeHigh": {"message": "All time high"}, "allTimeLow": {"message": "All time low"}, "allowNotifications": {"message": "Allow notifications"}, "allowWithdrawAndSpend": {"message": "Allow $1 to withdraw and spend up to the following amount:", "description": "The url of the site that requested permission to 'withdraw and spend'"}, "amount": {"message": "Amount"}, "amountReceived": {"message": "Amount Received"}, "amountSent": {"message": "Amount <PERSON>"}, "andForListItems": {"message": "$1, and $2", "description": "$1 is the first item, $2 is the last item in a list of items. Used in Snap Install Warning modal."}, "andForTwoItems": {"message": "$1 and $2", "description": "$1 is the first item, $2 is the second item. Used in Snap Install Warning modal."}, "appDescription": {"message": "The world's most trusted crypto wallet", "description": "The description of the application"}, "appName": {"message": "NeoNix Wallet", "description": "The name of the application"}, "appNameBeta": {"message": "NeoNix Wallet Beta", "description": "The name of the application (Beta)"}, "appNameFlask": {"message": "NeoNix Wallet Flask", "description": "The name of the application (Flask)"}, "apply": {"message": "Apply"}, "approve": {"message": "Approve spend limit"}, "approveButtonText": {"message": "Approve"}, "approveIncreaseAllowance": {"message": "Increase $1 spending cap", "description": "The token symbol that is being approved"}, "approveSpendingCap": {"message": "Approve $1 spending cap", "description": "The token symbol that is being approved"}, "approved": {"message": "Approved"}, "approvedOn": {"message": "Approved on $1", "description": "$1 is the approval date for a permission"}, "approvedOnForAccounts": {"message": "Approved on $1 for $2", "description": "$1 is the approval date for a permission. $2 is the AvatarGroup component displaying account images."}, "areYouSure": {"message": "Are you sure?"}, "asset": {"message": "<PERSON><PERSON>"}, "assetChartNoHistoricalPrices": {"message": "We could not fetch any historical data"}, "assetMultipleNFTsBalance": {"message": "$1 NFTs"}, "assetOptions": {"message": "Asset options"}, "assetSingleNFTBalance": {"message": "$1 NFT"}, "assets": {"message": "Assets"}, "assetsDescription": {"message": "Autodetect tokens in your wallet, display NFTs, and get batched account balance updates"}, "attemptToCancelSwapForFree": {"message": "Attempt to cancel swap for free"}, "attributes": {"message": "Attributes"}, "attributions": {"message": "Attributions"}, "auroraRpcDeprecationMessage": {"message": "The Infura RPC URL is no longer supporting Aurora."}, "authorizedPermissions": {"message": "You have authorized the following permissions"}, "autoDetectTokens": {"message": "Autodetect tokens"}, "autoDetectTokensDescription": {"message": "We use third-party APIs to detect and display new tokens sent to your wallet. Turn off if you don’t want the app to automatically pull data from those services. $1", "description": "$1 is a link to a support article"}, "autoLockTimeLimit": {"message": "Auto-lock timer (minutes)"}, "autoLockTimeLimitDescription": {"message": "Set the idle time in minutes before NeoNix will become locked."}, "average": {"message": "Average"}, "back": {"message": "Back"}, "backupAndSync": {"message": "Backup and sync"}, "backupAndSyncBasicFunctionalityNameMention": {"message": "basic functionality"}, "backupAndSyncEnable": {"message": "Turn on backup and sync"}, "backupAndSyncEnableConfirmation": {"message": "When you turn on backup and sync, you’re also turning on $1. Do you want to continue?", "description": "$1 is backupAndSyncBasicFunctionalityNameMention in bold."}, "backupAndSyncEnableDescription": {"message": "Backup and sync lets us store encrypted data for your custom settings and features. This keeps your NeoNix experience the same across devices and restores settings and features if you ever need to reinstall NeoNix. This doesn’t back up your Secret Recovery Phrase. $1.", "description": "$1 is link to the backup and sync privacy policy."}, "backupAndSyncEnableDescriptionUpdatePreferences": {"message": "You can update your preferences at any time in $1", "description": "$1 is a bolded text that highlights the path to the settings page."}, "backupAndSyncEnableDescriptionUpdatePreferencesPath": {"message": "Settings > Backup and sync."}, "backupAndSyncFeatureAccounts": {"message": "Accounts"}, "backupAndSyncManageWhatYouSync": {"message": "Manage what you sync"}, "backupAndSyncManageWhatYouSyncDescription": {"message": "Turn on what’s synced between your devices."}, "backupAndSyncPrivacyLink": {"message": "Learn how we protect your privacy"}, "backupAndSyncSlideDescription": {"message": "Back up your accounts and sync settings."}, "backupAndSyncSlideTitle": {"message": "Introducing backup and sync"}, "backupApprovalInfo": {"message": "This secret code is required to recover your wallet in case you lose your device, forget your password, have to re-install NeoNix, or want to access your wallet on another device."}, "backupApprovalNotice": {"message": "Back up your Secret Recovery Phrase to keep your wallet and funds secure."}, "backupKeyringSnapReminder": {"message": "Be sure you can access any accounts created by this Snap on your own before removing it"}, "backupNow": {"message": "Back up now"}, "balance": {"message": "Balance"}, "balanceOutdated": {"message": "Balance may be outdated"}, "baseFee": {"message": "Base fee"}, "basic": {"message": "Basic"}, "basicConfigurationBannerTitle": {"message": "Basic functionality is off"}, "basicConfigurationDescription": {"message": "NeoNix offers basic features like token details and gas settings through internet services. When you use internet services, your IP address is shared, in this case with NeoNix. This is just like when you visit any website. NeoNix uses this data temporarily and never sells your data. You can use a VPN or turn off these services, but it may affect your NeoNix experience. To learn more read our $1.", "description": "$1 is to be replaced by the message for privacyMsg, and will link to https://consensys.io/privacy-policy"}, "basicConfigurationLabel": {"message": "Basic functionality"}, "basicConfigurationModalCheckbox": {"message": "I understand and want to continue"}, "basicConfigurationModalDisclaimerOff": {"message": "This means you won't fully optimize your time on NeoNix. Basic features (like token details, optimal gas settings, and others) won't be available to you."}, "basicConfigurationModalDisclaimerOffAdditionalText": {"message": "Turning this off also disables all features within $1, and $2.", "description": "$1 and $2 are bold text for basicConfigurationModalDisclaimerOffAdditionalTextFeaturesFirst and basicConfigurationModalDisclaimerOffAdditionalTextFeaturesLast respectively"}, "basicConfigurationModalDisclaimerOffAdditionalTextFeaturesFirst": {"message": "security and privacy, backup and sync"}, "basicConfigurationModalDisclaimerOffAdditionalTextFeaturesLast": {"message": "notifications"}, "basicConfigurationModalDisclaimerOn": {"message": "To optimize your time on NeoNix, you’ll need to turn on this feature. Basic functions (like token details, optimal gas settings, and others) are important to the web3 experience."}, "basicConfigurationModalHeadingOff": {"message": "Turn off basic functionality"}, "basicConfigurationModalHeadingOn": {"message": "Turn on basic functionality"}, "bestPrice": {"message": "Best price"}, "beta": {"message": "Beta"}, "betaHeaderText": {"message": "This is a beta version. Please report bugs $1"}, "betaNeoNixVersion": {"message": "NeoNix Beta Version"}, "betaTerms": {"message": "Beta Terms of use"}, "billionAbbreviation": {"message": "B", "description": "Shortened form of 'billion'"}, "blockExplorerAccountAction": {"message": "Account", "description": "This is used with viewOnEtherscan and viewInExplorer e.g View Account in Explorer"}, "blockExplorerAssetAction": {"message": "<PERSON><PERSON>", "description": "This is used with viewOnEtherscan and viewInExplorer e.g View Asset in Explorer"}, "blockExplorerSwapAction": {"message": "<PERSON><PERSON><PERSON>", "description": "This is used with viewOnEtherscan e.g View Swap on Etherscan"}, "blockExplorerUrl": {"message": "Block explorer URL"}, "blockExplorerUrlDefinition": {"message": "The URL used as the block explorer for this network."}, "blockExplorerView": {"message": "View account at $1", "description": "$1 replaced by URL for custom block explorer"}, "blockaid": {"message": "Blockaid"}, "blockaidAlertDescriptionBlur": {"message": "If you continue, all the assets you’ve listed on Blur could be at risk."}, "blockaidAlertDescriptionMalicious": {"message": "You’re interacting with a malicious site. If you continue, you will lose your assets."}, "blockaidAlertDescriptionOpenSea": {"message": "If you continue, all the assets you’ve listed on OpenSea could be at risk."}, "blockaidAlertDescriptionOthers": {"message": "If you confirm this request, you could lose your assets. We recommend that you cancel this request."}, "blockaidAlertDescriptionTokenTransfer": {"message": "You’re sending your assets to a scammer. If you continue, you’ll lose those assets."}, "blockaidAlertDescriptionWithdraw": {"message": "If you confirm this request, you’re allowing a scammer to withdraw and spend your assets. You won’t get them back."}, "blockaidDescriptionApproveFarming": {"message": "If you approve this request, a third party known for scams might take all your assets."}, "blockaidDescriptionBlurFarming": {"message": "If you approve this request, someone can steal your assets listed on Blur."}, "blockaidDescriptionErrored": {"message": "Because of an error, we couldn't check for security alerts. Only continue if you trust every address involved."}, "blockaidDescriptionMaliciousDomain": {"message": "You're interacting with a malicious domain. If you approve this request, you might lose your assets."}, "blockaidDescriptionMightLoseAssets": {"message": "If you approve this request, you might lose your assets."}, "blockaidDescriptionSeaportFarming": {"message": "If you approve this request, someone can steal your assets listed on OpenSea."}, "blockaidDescriptionTransferFarming": {"message": "If you approve this request, a third party known for scams will take all your assets."}, "blockaidMessage": {"message": "Privacy preserving - no data is shared with third parties. Available on Arbitrum, Avalanche, BNB chain, Ethereum Mainnet, Linea, Optimism, Polygon, Base and Sepolia."}, "blockaidTitleDeceptive": {"message": "This is a deceptive request"}, "blockaidTitleMayNotBeSafe": {"message": "Be careful"}, "blockaidTitleSuspicious": {"message": "This is a suspicious request"}, "blockies": {"message": "Blockies"}, "borrowed": {"message": "Borrowed"}, "boughtFor": {"message": "Bought for"}, "bridge": {"message": "Bridge"}, "bridgeAllowSwappingOf": {"message": "Allow exact access to $1 $2 on $3 for bridging", "description": "Shows a user that they need to allow a token for swapping on their hardware wallet"}, "bridgeApproval": {"message": "Approve $1 for bridge", "description": "Used in the transaction display list to describe a transaction that is an approve call on a token that is to be bridged. $1 is the symbol of a token that has been approved."}, "bridgeApprovalWarning": {"message": "You are allowing access to the specified amount, $1 $2. The contract will not access any additional funds."}, "bridgeApprovalWarningForHardware": {"message": "You will need to allow access to $1 $2 for bridging, and then approve bridging to $2. This will require two separate confirmations."}, "bridgeBlockExplorerLinkCopied": {"message": "Block explorer link copied!"}, "bridgeCalculatingAmount": {"message": "Calculating..."}, "bridgeConfirmTwoTransactions": {"message": "You'll need to confirm 2 transactions on your hardware wallet:"}, "bridgeCreateSolanaAccount": {"message": "Create Solana account"}, "bridgeCreateSolanaAccountDescription": {"message": "To swap to the Solana network, you need an account and receiving address."}, "bridgeCreateSolanaAccountTitle": {"message": "You'll need a Solana account first."}, "bridgeDetailsTitle": {"message": "Bridge details", "description": "Title for the modal showing details about a bridge transaction."}, "bridgeEnterAmount": {"message": "Select amount"}, "bridgeEnterAmountAndSelectAccount": {"message": "Enter amount and select destination account"}, "bridgeExplorerLinkViewOn": {"message": "View on $1"}, "bridgeFetchNewQuotes": {"message": "Fetch a new one?"}, "bridgeFrom": {"message": "Bridge from"}, "bridgeFromTo": {"message": "Bridge $1 $2 to $3", "description": "Tells a user that they need to confirm on their hardware wallet a bridge. $1 is amount of source token, $2 is the source network, and $3 is the destination network"}, "bridgeGasFeesSplit": {"message": "Any network fee quoted on the previous screen includes both transactions and will be split."}, "bridgeNetCost": {"message": "Net cost"}, "bridgeQuoteExpired": {"message": "Your quote timed out."}, "bridgeSelectDestinationAccount": {"message": "Select destination account"}, "bridgeSelectNetwork": {"message": "Select network"}, "bridgeSelectTokenAmountAndAccount": {"message": "Select token, amount and destination account"}, "bridgeSelectTokenAndAmount": {"message": "Select token and amount"}, "bridgeSolanaAccountCreated": {"message": "Solana account created"}, "bridgeStatusComplete": {"message": "Complete", "description": "Status text indicating a bridge transaction has successfully completed."}, "bridgeStatusFailed": {"message": "Failed", "description": "Status text indicating a bridge transaction has failed."}, "bridgeStatusInProgress": {"message": "In Progress", "description": "Status text indicating a bridge transaction is currently processing."}, "bridgeStepActionBridgeComplete": {"message": "$1 received on $2", "description": "$1 is the amount of the destination asset, $2 is the name of the destination network"}, "bridgeStepActionBridgePending": {"message": "Receiving $1 on $2", "description": "$1 is the amount of the destination asset, $2 is the name of the destination network"}, "bridgeStepActionSwapComplete": {"message": "Swapped $1 for $2", "description": "$1 is the amount of the source asset, $2 is the amount of the destination asset"}, "bridgeStepActionSwapPending": {"message": "Swapping $1 for $2", "description": "$1 is the amount of the source asset, $2 is the amount of the destination asset"}, "bridgeTerms": {"message": "Terms"}, "bridgeTimingMinutes": {"message": "$1 min", "description": "$1 is the ticker symbol of a an asset the user is being prompted to purchase"}, "bridgeTo": {"message": "Bridge to"}, "bridgeToChain": {"message": "Bridge to $1"}, "bridgeTokenCannotVerifyDescription": {"message": "If you added this token manually, make sure you are aware of the risks to your funds before you bridge."}, "bridgeTokenCannotVerifyTitle": {"message": "We can't verify this token."}, "bridgeTransactionProgress": {"message": "Transaction $1 of 2"}, "bridgeTxDetailsBridging": {"message": "Bridging"}, "bridgeTxDetailsDelayedDescription": {"message": "Reach out to"}, "bridgeTxDetailsDelayedDescriptionSupport": {"message": "NeoNix Support"}, "bridgeTxDetailsDelayedTitle": {"message": "Has it been longer than 3 hours?"}, "bridgeTxDetailsNonce": {"message": "<PERSON><PERSON>"}, "bridgeTxDetailsStatus": {"message": "Status"}, "bridgeTxDetailsTimestamp": {"message": "Time stamp"}, "bridgeTxDetailsTimestampValue": {"message": "$1 at $2", "description": "$1 is the date, $2 is the time"}, "bridgeTxDetailsTokenAmountOnChain": {"message": "$1 $2 on", "description": "$1 is the amount of the token, $2 is the ticker symbol of the token"}, "bridgeTxDetailsTotalGasFee": {"message": "Total gas fee"}, "bridgeTxDetailsYouReceived": {"message": "You received"}, "bridgeTxDetailsYouSent": {"message": "You sent"}, "bridgeValidationInsufficientGasMessage": {"message": "You don't have enough $1 to pay the gas fee for this bridge. Enter a smaller amount or buy more $1."}, "bridgeValidationInsufficientGasTitle": {"message": "More $1 needed for gas"}, "bridging": {"message": "Bridging"}, "browserNotSupported": {"message": "Your browser is not supported..."}, "buildContactList": {"message": "Build your contact list"}, "builtAroundTheWorld": {"message": "NeoNix is designed and built around the world."}, "bulletpoint": {"message": "·"}, "busy": {"message": "Busy"}, "buyAndSell": {"message": "Buy/Sell"}, "buyMoreAsset": {"message": "Buy more $1", "description": "$1 is the ticker symbol of a an asset the user is being prompted to purchase"}, "buyNow": {"message": "Buy Now"}, "bytes": {"message": "Bytes"}, "canToggleInSettings": {"message": "You can re-enable this notification in Settings > Alerts."}, "cancel": {"message": "Cancel"}, "cancelPopoverTitle": {"message": "Cancel transaction"}, "cancelSpeedUpLabel": {"message": "This gas fee will $1 the original.", "description": "$1 is text 'replace' in bold"}, "cancelSpeedUpTransactionTooltip": {"message": "To $1 a transaction the gas fee must be increased by at least 10% for it to be recognized by the network.", "description": "$1 is string 'cancel' or 'speed up'"}, "cancelled": {"message": "Cancelled"}, "chainId": {"message": "Chain ID"}, "chainIdDefinition": {"message": "The chain ID used to sign transactions for this network."}, "chainIdExistsErrorMsg": {"message": "This Chain ID is currently used by the $1 network."}, "chainListReturnedDifferentTickerSymbol": {"message": "This token symbol doesn't match the network name or chain ID entered. Many popular tokens use similar symbols, which scammers can use to trick you into sending them a more valuable token in return. Verify everything before you continue."}, "chooseYourNetwork": {"message": "Choose your network"}, "chooseYourNetworkDescription": {"message": "When you use our default settings and configurations, we use Infura as our default remote procedure call (RPC) provider to offer the most reliable and private access to Ethereum data we can. In limited cases, we may use other RPC providers in order to provide the best experience for our users. You can choose your own RPC, but remember that any RPC will receive your IP address and Ethereum wallet to make transactions. To learn more about how Infura handles data for EVM accounts, read our $1, and for Solana accounts, $2.", "description": "$1 is a link to the privacy policy, $2 is a link to Solana accounts support"}, "chooseYourNetworkDescriptionCallToAction": {"message": "click here"}, "chromeRequiredForHardwareWallets": {"message": "You need to use NeoNix on Google Chrome in order to connect to your Hardware Wallet."}, "circulatingSupply": {"message": "Circulating supply"}, "clear": {"message": "Clear"}, "clearActivity": {"message": "Clear activity and nonce data"}, "clearActivityButton": {"message": "Clear activity tab data"}, "clearActivityDescription": {"message": "This resets the account's nonce and erases data from the activity tab in your wallet. Only the current account and network will be affected. Your balances and incoming transactions won't change."}, "click": {"message": "Click"}, "clickToConnectLedgerViaWebHID": {"message": "Click here to connect your Ledger via WebHID", "description": "Text that can be clicked to open a browser popup for connecting the ledger device via webhid"}, "close": {"message": "Close"}, "closeExtension": {"message": "Close extension"}, "closeWindowAnytime": {"message": "You may close this window anytime."}, "coingecko": {"message": "CoinGecko"}, "collectionName": {"message": "Collection name"}, "comboNoOptions": {"message": "No options found", "description": "Default text shown in the combo field dropdown if no options."}, "concentratedSupplyDistributionDescription": {"message": "The majority of the token's supply is held by the top token holders, posing a risk of centralized price manipulation"}, "concentratedSupplyDistributionTitle": {"message": "Concentrated Supply Distribution"}, "configureSnapPopupDescription": {"message": "You're now leaving NeoNix to configure this snap."}, "configureSnapPopupInstallDescription": {"message": "You're now leaving NeoNix to install this snap."}, "configureSnapPopupInstallTitle": {"message": "Install snap"}, "configureSnapPopupLink": {"message": "Click this link to continue:"}, "configureSnapPopupTitle": {"message": "Configure snap"}, "confirm": {"message": "Confirm"}, "confirmAccountTypeSmartContract": {"message": "Smart account"}, "confirmAccountTypeStandard": {"message": "Standard account"}, "confirmAlertModalAcknowledgeMultiple": {"message": "I have acknowledged the alerts and still want to proceed"}, "confirmAlertModalAcknowledgeSingle": {"message": "I have acknowledged the alert and still want to proceed"}, "confirmFieldPaymaster": {"message": "Fee paid by"}, "confirmFieldTooltipPaymaster": {"message": "The fee for this transaction will be paid by the paymaster smart contract."}, "confirmGasFeeTokenBalance": {"message": "Bal:"}, "confirmGasFeeTokenInsufficientBalance": {"message": "Insufficient funds"}, "confirmGasFeeTokenNeoNixFee": {"message": "Includes $1 fee"}, "confirmGasFeeTokenModalNativeToggleNeoNix": {"message": "NeoNix is supplementing the balance to complete this transaction."}, "confirmGasFeeTokenModalNativeToggleWallet": {"message": "Pay for network fee using the balance in your wallet."}, "confirmGasFeeTokenModalPayETH": {"message": "Pay with ETH"}, "confirmGasFeeTokenModalPayToken": {"message": "Pay with other tokens"}, "confirmGasFeeTokenModalTitle": {"message": "Select a token"}, "confirmGasFeeTokenToast": {"message": "You're paying this network fee with $1"}, "confirmGasFeeTokenTooltip": {"message": "This is paid to the network to process your transaction. It includes a $1 NeoNix fee for non-ETH tokens or pre-funded ETH."}, "confirmInfoAccountNow": {"message": "Now"}, "confirmInfoSwitchingTo": {"message": "Switching To"}, "confirmNestedTransactionTitle": {"message": "Transaction $1"}, "confirmPassword": {"message": "Confirm password"}, "confirmPasswordPlaceholder": {"message": "Re-enter your password"}, "confirmRecoveryPhrase": {"message": "Confirm Secret Recovery Phrase"}, "confirmRecoveryPhraseDetails": {"message": "Select the missing words in the correct order."}, "confirmRecoveryPhraseTitle": {"message": "Confirm your Secret Recovery Phrase"}, "confirmSimulationApprove": {"message": "You approve"}, "confirmSrpErrorDescription": {"message": "Double-check your Secret Recovery Phrase and try again."}, "confirmSrpErrorTitle": {"message": "Not quite right"}, "confirmSrpSuccessDescription": {"message": "That’s right! And remember: never share this phrase with anyone, ever."}, "confirmSrpSuccessTitle": {"message": "Perfect"}, "confirmTitleAccountTypeSwitch": {"message": "Account update"}, "confirmTitleApproveTransactionNFT": {"message": "<PERSON><PERSON><PERSON> request"}, "confirmTitleDeployContract": {"message": "Deploy a contract"}, "confirmTitleDescApproveTransaction": {"message": "This site wants permission to withdraw your NFTs"}, "confirmTitleDescDelegationRevoke": {"message": "You're switching back to a standard account (EOA)."}, "confirmTitleDescDelegationUpgrade": {"message": "You're switching to a smart account"}, "confirmTitleDescDeployContract": {"message": "This site wants you to deploy a contract"}, "confirmTitleDescERC20ApproveTransaction": {"message": "This site wants permission to withdraw your tokens"}, "confirmTitleDescPermitSignature": {"message": "This site wants permission to spend your tokens."}, "confirmTitleDescSIWESignature": {"message": "A site wants you to sign in to prove you own this account."}, "confirmTitleDescSign": {"message": "Review request details before you confirm."}, "confirmTitlePermitTokens": {"message": "Spending cap request"}, "confirmTitleRevokeApproveTransaction": {"message": "Remove permission"}, "confirmTitleSIWESignature": {"message": "Sign-in request"}, "confirmTitleSetApprovalForAllRevokeTransaction": {"message": "Remove permission"}, "confirmTitleSignature": {"message": "Signature request"}, "confirmTitleTransaction": {"message": "Transaction request"}, "confirmationAlertDetails": {"message": "To protect your assets, we suggest you reject the request."}, "confirmationAlertModalTitleDescription": {"message": "Your assets may be at risk"}, "confirmed": {"message": "Confirmed"}, "confusableUnicode": {"message": "'$1' is similar to '$2'."}, "confusableZeroWidthUnicode": {"message": "Zero-width character found."}, "confusingEnsDomain": {"message": "We have detected a confusable character in the ENS name. Check the ENS name to avoid a potential scam."}, "connect": {"message": "Connect"}, "connectAccount": {"message": "Connect account"}, "connectAccountOrCreate": {"message": "Connect account or create new"}, "connectAccounts": {"message": "Connect accounts"}, "connectAnAccountHeader": {"message": "Connect an account"}, "connectManually": {"message": "Manually connect to current site"}, "connectMoreAccounts": {"message": "Connect more accounts"}, "connectSnap": {"message": "Connect $1", "description": "$1 is the snap for which a connection is being requested."}, "connectWithNeoNix": {"message": "Connect with NeoNix"}, "connectedAccounts": {"message": "Connected accounts"}, "connectedAccountsDescriptionPlural": {"message": "You have $1 accounts connected to this site.", "description": "$1 is the number of accounts"}, "connectedAccountsDescriptionSingular": {"message": "You have 1 account connected to this site."}, "connectedAccountsEmptyDescription": {"message": "NeoNix is not connected to this site. To connect to a web3 site, find and click the connect button."}, "connectedAccountsListTooltip": {"message": "$1 can see the account balance, address, activity, and suggest transactions to approve for connected accounts.", "description": "$1 is the origin name"}, "connectedAccountsToast": {"message": "Connected accounts updated"}, "connectedSites": {"message": "Connected sites"}, "connectedSitesAndSnaps": {"message": "Connected sites and Snaps"}, "connectedSitesDescription": {"message": "$1 is connected to these sites. They can view your account address.", "description": "$1 is the account name"}, "connectedSitesEmptyDescription": {"message": "$1 is not connected to any sites.", "description": "$1 is the account name"}, "connectedSnapAndNoAccountDescription": {"message": "NeoNix Wallet is connected to this site, but no accounts are connected yet"}, "connectedSnaps": {"message": "Connected Snaps"}, "connectedWithAccount": {"message": "$1 accounts connected", "description": "$1 represents account length"}, "connectedWithAccountName": {"message": "Connected with $1", "description": "$1 represents account name"}, "connectedWithNetwork": {"message": "$1 networks connected", "description": "$1 represents network length"}, "connectedWithNetworkName": {"message": "Connected with $1", "description": "$1 represents network name"}, "connecting": {"message": "Connecting"}, "connectingTo": {"message": "Connecting to $1"}, "connectingToDeprecatedNetwork": {"message": "'$1' is being phased out and may not work. Try another network."}, "connectingToGoerli": {"message": "Connecting to Goerli test network"}, "connectingToLineaGoerli": {"message": "Connecting to Linea Goerli test network"}, "connectingToLineaMainnet": {"message": "Connecting to Linea Mainnet"}, "connectingToLineaSepolia": {"message": "Connecting to Linea Sepolia test network"}, "connectingToMainnet": {"message": "Connecting to Ethereum Mainnet"}, "connectingToSepolia": {"message": "Connecting to Sepolia test network"}, "connectionDescription": {"message": "Connect this website with NeoNix"}, "connectionFailed": {"message": "Connection failed"}, "connectionFailedDescription": {"message": "Fetching of $1 failed, check your network and try again.", "description": "$1 is the name of the snap being fetched."}, "connectionPopoverDescription": {"message": "To connect to a site, select the connect button. NeoNix can only connect to web3 sites."}, "connectionRequest": {"message": "Connection request"}, "contactUs": {"message": "Contact us"}, "contacts": {"message": "Contacts"}, "contentFromSnap": {"message": "Content from $1", "description": "$1 represents the name of the snap"}, "continue": {"message": "Continue"}, "contract": {"message": "Contract"}, "contractAddress": {"message": "Contract address"}, "contractAddressError": {"message": "You are sending tokens to the token's contract address. This may result in the loss of these tokens."}, "contractDeployment": {"message": "Contract deployment"}, "contractInteraction": {"message": "Contract interaction"}, "convertTokenToNFTDescription": {"message": "We've detected that this asset is an NFT. NeoNix now has full native support for NFTs. Would you like to remove it from your token list and add it as an NFT?"}, "convertTokenToNFTExistDescription": {"message": "We’ve detected that this asset has been added as an NFT. Would you like to remove it from your token list?"}, "coolWallet": {"message": "CoolWallet"}, "copiedExclamation": {"message": "Copied."}, "copyAddress": {"message": "Copy address to clipboard"}, "copyAddressShort": {"message": "Copy address"}, "copyPrivateKey": {"message": "Copy private key"}, "copyToClipboard": {"message": "Copy to clipboard"}, "copyTransactionId": {"message": "Copy transaction ID"}, "create": {"message": "Create"}, "createNewAccountHeader": {"message": "Create a new account"}, "createPassword": {"message": "Create password"}, "createSnapAccountDescription": {"message": "$1 wants to add a new account to NeoNix."}, "createSnapAccountTitle": {"message": "Create account"}, "createSolanaAccount": {"message": "Create Solana account"}, "creatorAddress": {"message": "Creator address"}, "crossChainSwapsLink": {"message": "Swap across networks with NeoNix Portfolio"}, "crossChainSwapsLinkNative": {"message": "Swap across networks with Bridge"}, "cryptoCompare": {"message": "CryptoCompare"}, "currencyConversion": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "currencyRateCheckToggle": {"message": "Show balance and token price checker"}, "currencyRateCheckToggleDescription": {"message": "We use $1 and $2 APIs to display your balance and token price. $3", "description": "$1 represents Coingecko, $2 represents CryptoCompare and $3 represents Privacy Policy"}, "currencySymbol": {"message": "Currency symbol"}, "currencySymbolDefinition": {"message": "The ticker symbol displayed for this network’s currency."}, "currentAccountNotConnected": {"message": "Your current account is not connected"}, "currentExtension": {"message": "Current extension page"}, "currentLanguage": {"message": "Current language"}, "currentNetwork": {"message": "Current network", "description": "Speicifies to token network filter to filter by current Network. Will render when network nickname is not available"}, "currentRpcUrlDeprecated": {"message": "The current rpc url for this network has been deprecated."}, "currentTitle": {"message": "Current:"}, "currentlyUnavailable": {"message": "Unavailable on this network"}, "curveHighGasEstimate": {"message": "Aggressive gas estimate graph"}, "curveLowGasEstimate": {"message": "Low gas estimate graph"}, "curveMediumGasEstimate": {"message": "Market gas estimate graph"}, "custom": {"message": "Advanced"}, "customGasSettingToolTipMessage": {"message": "Use $1 to customize the gas price. This can be confusing if you aren’t familiar. Interact at your own risk.", "description": "$1 is key 'advanced' (text: 'Advanced') separated here so that it can be passed in with bold font-weight"}, "customSlippage": {"message": "Custom"}, "customSpendLimit": {"message": "Custom spend limit"}, "customToken": {"message": "Custom token"}, "customTokenWarningInNonTokenDetectionNetwork": {"message": "Token detection is not available on this network yet. Please import token manually and make sure you trust it. Learn about $1"}, "customTokenWarningInTokenDetectionNetwork": {"message": "Anyone can create a token, including creating fake versions of existing tokens. Learn about $1"}, "customTokenWarningInTokenDetectionNetworkWithTDOFF": {"message": "Make sure you trust a token before you import it. Learn how to avoid $1. You can also enable token detection $2."}, "customerSupport": {"message": "customer support"}, "customizeYourNotifications": {"message": "Customize your notifications"}, "customizeYourNotificationsText": {"message": "Turn on the types of notifications you want to receive:"}, "dappSuggested": {"message": "Site suggested"}, "dappSuggestedGasSettingToolTipMessage": {"message": "$1 has suggested this price.", "description": "$1 is url for the dapp that has suggested gas settings"}, "dappSuggestedHigh": {"message": "Site suggested"}, "dappSuggestedHighShortLabel": {"message": "Site (high)"}, "dappSuggestedShortLabel": {"message": "Site"}, "dappSuggestedTooltip": {"message": "$1 has recommended this price.", "description": "$1 represents the Dapp's origin"}, "darkTheme": {"message": "Dark"}, "data": {"message": "Data"}, "dataCollectionForMarketing": {"message": "Data collection for marketing"}, "dataCollectionForMarketingDescription": {"message": "We'll use MetaMetrics to learn how you interact with our marketing communications. We may share relevant news (like product features and other materials)."}, "dataCollectionWarningPopoverButton": {"message": "Okay"}, "dataCollectionWarningPopoverDescription": {"message": "You turned off data collection for our marketing purposes. This only applies to this device. If you use NeoNix on other devices, make sure to opt out there as well."}, "dataUnavailable": {"message": "data unavailable"}, "dateCreated": {"message": "Date created"}, "dcent": {"message": "<PERSON><PERSON><PERSON>nt"}, "debitCreditPurchaseOptions": {"message": "Debit or credit card purchase options"}, "decimal": {"message": "Token decimal"}, "decimalsMustZerotoTen": {"message": "Decimals must be at least 0, and not over 36."}, "decrypt": {"message": "Decrypt"}, "decryptCopy": {"message": "Copy encrypted message"}, "decryptInlineError": {"message": "This message cannot be decrypted due to error: $1", "description": "$1 is error message"}, "decryptMessageNotice": {"message": "$1 would like to read this message to complete your action", "description": "$1 is the web3 site name"}, "decryptNeoNix": {"message": "Decrypt message"}, "decryptRequest": {"message": "Decrypt request"}, "defaultRpcUrl": {"message": "Default RPC URL"}, "defaultSettingsSubTitle": {"message": "NeoNix uses default settings to best balance safety and ease of use. Change these settings to further increase your privacy."}, "defaultSettingsTitle": {"message": "Default privacy settings"}, "defi": {"message": "<PERSON><PERSON><PERSON>"}, "defiTabErrorContent": {"message": "Try visiting again later."}, "defiTabErrorTitle": {"message": "We could not load this page."}, "delete": {"message": "Delete"}, "deleteContact": {"message": "Delete contact"}, "deleteMetaMetricsData": {"message": "Delete MetaMetrics data"}, "deleteMetaMetricsDataDescription": {"message": "This will delete historical MetaMetrics data associated with your use on this device. Your wallet and accounts will remain exactly as they are now after this data has been deleted. This process may take up to 30 days. View our $1.", "description": "$1 will have text saying Privacy Policy "}, "deleteMetaMetricsDataErrorDesc": {"message": "This request can't be completed right now due to an analytics system server issue, please try again later"}, "deleteMetaMetricsDataErrorTitle": {"message": "We are unable to delete this data right now"}, "deleteMetaMetricsDataModalDesc": {"message": "We are about to remove all your MetaMetrics data. Are you sure?"}, "deleteMetaMetricsDataModalTitle": {"message": "Delete MetaMetrics data?"}, "deleteMetaMetricsDataRequestedDescription": {"message": "You initiated this action on $1. This process can take up to 30 days. View the $2", "description": "$1 will be the date on which teh deletion is requested and $2 will have text saying Privacy Policy "}, "deleteNetworkIntro": {"message": "If you delete this network, you will need to add it again to view your assets in this network"}, "deleteNetworkTitle": {"message": "Delete $1 network?", "description": "$1 represents the name of the network"}, "depositCrypto": {"message": "Deposit crypto from another account with a wallet address or QR code."}, "deprecatedGoerliNtwrkMsg": {"message": "Because of updates to the Ethereum system, the Goerli test network will be phased out soon."}, "deprecatedNetwork": {"message": "This network is deprecated"}, "deprecatedNetworkButtonMsg": {"message": "Got it"}, "deprecatedNetworkDescription": {"message": "The network you're trying to connect to is no longer supported by NeoNix. $1"}, "description": {"message": "Description"}, "descriptionFromSnap": {"message": "Description from $1", "description": "$1 represents the name of the snap"}, "destinationAccountPickerNoEligible": {"message": "No eligible accounts found"}, "destinationAccountPickerNoMatching": {"message": "No matching accounts found"}, "destinationAccountPickerReceiveAt": {"message": "Receive at"}, "destinationAccountPickerSearchPlaceholderToMainnet": {"message": "Receiving address or ENS"}, "destinationAccountPickerSearchPlaceholderToSolana": {"message": "Receiving address"}, "destinationTransactionIdLabel": {"message": "Destination Tx ID", "description": "Label for the destination transaction ID field."}, "details": {"message": "Details"}, "developerOptions": {"message": "Developer Options"}, "disabledGasOptionToolTipMessage": {"message": "“$1” is disabled because it does not meet the minimum of a 10% increase from the original gas fee.", "description": "$1 is gas estimate type which can be market or aggressive"}, "disconnect": {"message": "Disconnect"}, "disconnectAllAccounts": {"message": "Disconnect all accounts"}, "disconnectAllAccountsConfirmationDescription": {"message": "Are you sure you want to disconnect? You may lose site functionality."}, "disconnectAllAccountsText": {"message": "accounts"}, "disconnectAllDescriptionText": {"message": "If you disconnect from this site, you’ll need to reconnect your accounts and networks to use this site again."}, "disconnectAllSnapsText": {"message": "Snaps"}, "disconnectMessage": {"message": "This will disconnect you from this site"}, "disconnectPrompt": {"message": "Disconnect $1"}, "disconnectThisAccount": {"message": "Disconnect this account"}, "disconnectedAllAccountsToast": {"message": "All accounts disconnected from $1", "description": "$1 is name of the dapp`"}, "disconnectedSingleAccountToast": {"message": "$1 disconnected from $2", "description": "$1 is name of the name and $2 represents the dapp name`"}, "discover": {"message": "Discover"}, "discoverSnaps": {"message": "Discover Snaps", "description": "Text that links to the Snaps website. Displayed in a banner on Snaps list page in settings."}, "dismiss": {"message": "<PERSON><PERSON><PERSON>"}, "dismissReminderDescriptionField": {"message": "Turn this on to dismiss the Secret Recovery Phrase backup reminder message. We highly recommend that you back up your Secret Recovery Phrase to avoid loss of funds"}, "dismissReminderField": {"message": "Dismiss Secret Recovery Phrase backup reminder"}, "dismissSmartAccountSuggestionEnabledDescription": {"message": "Turn this on to no longer see the \"Switch to Smart Account\" suggestion on any account. Smart accounts unlocks faster transactions, lower network fees and more flexibility on payment for those."}, "dismissSmartAccountSuggestionEnabledTitle": {"message": "Dism<PERSON> \"Switch to Smart Account\" suggestion"}, "displayNftMedia": {"message": "Display NFT media"}, "displayNftMediaDescription": {"message": "Displaying NFT media and data exposes your IP address to OpenSea or other third parties. This can allow attackers to associate your IP address with your Ethereum address. NFT autodetection relies on this setting, and won't be available when this is turned off."}, "doNotShare": {"message": "Do not share this with anyone"}, "domain": {"message": "Domain"}, "done": {"message": "Done"}, "dontShowThisAgain": {"message": "Don't show this again"}, "downArrow": {"message": "down arrow"}, "downloadGoogleChrome": {"message": "Download Google Chrome"}, "downloadNow": {"message": "Download Now"}, "downloadStateLogs": {"message": "Download state logs"}, "dragAndDropBanner": {"message": "You can drag networks to reorder them. "}, "dropped": {"message": "Dropped"}, "duplicateContactTooltip": {"message": "This contact name collides with an existing account or contact"}, "duplicateContactWarning": {"message": "You have duplicate contacts"}, "durationSuffixDay": {"message": "D", "description": "Shortened form of 'day'"}, "durationSuffixHour": {"message": "H", "description": "Shortened form of 'hour'"}, "durationSuffixMillisecond": {"message": "MS", "description": "Shortened form of 'millisecond'"}, "durationSuffixMinute": {"message": "M", "description": "Shortened form of 'minute'"}, "durationSuffixMonth": {"message": "M", "description": "Shortened form of 'month'"}, "durationSuffixSecond": {"message": "S", "description": "Shortened form of 'second'"}, "durationSuffixWeek": {"message": "W", "description": "Shortened form of 'week'"}, "durationSuffixYear": {"message": "Y", "description": "Shortened form of 'year'"}, "earn": {"message": "<PERSON><PERSON><PERSON>"}, "edit": {"message": "Edit"}, "editANickname": {"message": "Edit nickname"}, "editAccountName": {"message": "Edit account name"}, "editAccounts": {"message": "Edit accounts"}, "editAddressNickname": {"message": "Edit address nickname"}, "editCancellationGasFeeModalTitle": {"message": "Edit cancellation gas fee"}, "editContact": {"message": "Edit contact"}, "editGasFeeModalTitle": {"message": "Edit gas fee"}, "editGasLimitOutOfBounds": {"message": "Gas limit must be at least $1"}, "editGasLimitOutOfBoundsV2": {"message": "Gas limit must be greater than $1 and less than $2", "description": "$1 is the minimum limit for gas and $2 is the maximum limit"}, "editGasLimitTooltip": {"message": "Gas limit is the maximum units of gas you are willing to use. Units of gas are a multiplier to “Max priority fee” and “Max fee”."}, "editGasMaxBaseFeeGWEIImbalance": {"message": "Max base fee cannot be lower than priority fee"}, "editGasMaxBaseFeeHigh": {"message": "Max base fee is higher than necessary"}, "editGasMaxBaseFeeLow": {"message": "Max base fee is low for current network conditions"}, "editGasMaxFeeHigh": {"message": "Max fee is higher than necessary"}, "editGasMaxFeeLow": {"message": "Max fee too low for network conditions"}, "editGasMaxFeePriorityImbalance": {"message": "Max fee cannot be lower than max priority fee"}, "editGasMaxPriorityFeeBelowMinimum": {"message": "Max priority fee must be greater than 0 GWEI"}, "editGasMaxPriorityFeeBelowMinimumV2": {"message": "Priority fee must be greater than 0."}, "editGasMaxPriorityFeeHigh": {"message": "Max priority fee is higher than necessary. You may pay more than needed."}, "editGasMaxPriorityFeeHighV2": {"message": "Priority fee is higher than necessary. You may pay more than needed"}, "editGasMaxPriorityFeeLow": {"message": "Max priority fee is low for current network conditions"}, "editGasMaxPriorityFeeLowV2": {"message": "Priority fee is low for current network conditions"}, "editGasPriceTooLow": {"message": "Gas price must be greater than 0"}, "editGasPriceTooltip": {"message": "This network requires a “Gas price” field when submitting a transaction. Gas price is the amount you will pay pay per unit of gas."}, "editGasSubTextFeeLabel": {"message": "Max fee:"}, "editGasTitle": {"message": "Edit priority"}, "editGasTooLow": {"message": "Unknown processing time"}, "editInPortfolio": {"message": "Edit in Portfolio"}, "editNetworkLink": {"message": "edit the original network"}, "editNetworksTitle": {"message": "Edit networks"}, "editNonceField": {"message": "Edit nonce"}, "editNonceMessage": {"message": "This is an advanced feature, use cautiously."}, "editPermission": {"message": "Edit permission"}, "editPermissions": {"message": "Edit permissions"}, "editSpeedUpEditGasFeeModalTitle": {"message": "Edit speed up gas fee"}, "editSpendingCap": {"message": "Edit spending cap"}, "editSpendingCapAccountBalance": {"message": "Account balance: $1 $2"}, "editSpendingCapDesc": {"message": "Enter the amount that you feel comfortable being spent on your behalf."}, "editSpendingCapError": {"message": "The spending cap can’t exceed $1 decimal digits. Remove decimal digits to continue."}, "editSpendingCapSpecialCharError": {"message": "Enter numbers only"}, "enableAutoDetect": {"message": " Enable autodetect"}, "enableFromSettings": {"message": " Enable it from Settings."}, "enableIt": {"message": " Enable it"}, "enableSnap": {"message": "Enable"}, "enableToken": {"message": "enable $1", "description": "$1 is a token symbol, e.g. ETH"}, "enabled": {"message": "Enabled"}, "enabledNetworks": {"message": "Enabled networks"}, "encryptionPublicKeyNotice": {"message": "$1 would like your public encryption key. By consenting, this site will be able to compose encrypted messages to you.", "description": "$1 is the web3 site name"}, "encryptionPublicKeyRequest": {"message": "Request encryption public key"}, "endpointReturnedDifferentChainId": {"message": "The RPC URL you have entered returned a different chain ID ($1).", "description": "$1 is the return value of eth_chainId from an RPC endpoint"}, "enhancedTokenDetectionAlertMessage": {"message": "Enhanced token detection is currently available on $1. $2"}, "ensDomainsSettingDescriptionIntroduction": {"message": "NeoNix lets you see ENS domains right in your browser's address bar. Here's how it works:"}, "ensDomainsSettingDescriptionOutroduction": {"message": "Keep in mind that using this feature exposes your IP address to IPFS third-party services."}, "ensDomainsSettingDescriptionPart1": {"message": "NeoNix checks with Ethereum's ENS contract to find the code connected to the ENS name."}, "ensDomainsSettingDescriptionPart2": {"message": "If the code links to IPFS, you can see the content associated with it (usually a website)."}, "ensDomainsSettingTitle": {"message": "Show ENS domains in address bar"}, "ensUnknownError": {"message": "ENS lookup failed."}, "enterANameToIdentifyTheUrl": {"message": "Enter a name to identify the URL"}, "enterChainId": {"message": "Enter Chain ID"}, "enterMaxSpendLimit": {"message": "Enter max spend limit"}, "enterNetworkName": {"message": "Enter network name"}, "enterOptionalPassword": {"message": "Enter optional password"}, "enterPassword": {"message": "Enter password"}, "enterPasswordContinue": {"message": "Enter password to continue"}, "enterRpcUrl": {"message": "Enter RPC URL"}, "enterSymbol": {"message": "Enter symbol"}, "enterTokenNameOrAddress": {"message": "Enter token name or paste address"}, "enterYourPassword": {"message": "Enter your password"}, "errorCode": {"message": "Code: $1", "description": "Displayed error code for debugging purposes. $1 is the error code"}, "errorGettingSafeChainList": {"message": "Error while getting safe chain list, please continue with caution."}, "errorMessage": {"message": "Message: $1", "description": "Displayed error message for debugging purposes. $1 is the error message"}, "errorName": {"message": "Code: $1", "description": "Displayed error name for debugging purposes. $1 is the error name"}, "errorPageContactSupport": {"message": "Contact support", "description": "Button for contact MM support"}, "errorPageDescribeUsWhatHappened": {"message": "Describe what happened", "description": "<PERSON><PERSON> for submitting report to sentry"}, "errorPageInfo": {"message": "Your information can’t be shown. Don’t worry, your wallet and funds are safe.", "description": "Information banner shown in the error page"}, "errorPageMessageTitle": {"message": "Error message", "description": "Title for description, which is displayed for debugging purposes"}, "errorPageSentryFormTitle": {"message": "Describe what happened", "description": "In sentry feedback form, The title at the top of the feedback form."}, "errorPageSentryMessagePlaceholder": {"message": "Sharing details like how we can reproduce the bug will help us fix the problem.", "description": "In sentry feedback form, The placeholder for the feedback description input field."}, "errorPageSentrySuccessMessageText": {"message": "Thanks! We will take a look soon.", "description": "In sentry feedback form, The message displayed after a successful feedback submission."}, "errorPageTitle": {"message": "NeoNix encountered an error", "description": "Title of generic error page"}, "errorPageTryAgain": {"message": "Try again", "description": "<PERSON><PERSON> for try again"}, "errorStack": {"message": "Stack:", "description": "Title for error stack, which is displayed for debugging purposes"}, "errorWhileConnectingToRPC": {"message": "Error while connecting to the custom network."}, "errorWithSnap": {"message": "Error with $1", "description": "$1 represents the name of the snap"}, "estimatedFee": {"message": "Estimated fee"}, "estimatedFeeTooltip": {"message": "Amount paid to process the transaction on network."}, "ethGasPriceFetchWarning": {"message": "Backup gas price is provided as the main gas estimation service is unavailable right now."}, "ethereumProviderAccess": {"message": "Grant Ethereum provider access to $1", "description": "The parameter is the name of the requesting origin"}, "ethereumPublicAddress": {"message": "Ethereum public address"}, "etherscan": {"message": "Etherscan"}, "etherscanView": {"message": "View account on Etherscan"}, "etherscanViewOn": {"message": "View on Etherscan"}, "existingChainId": {"message": "The information you have entered is associated with an existing chain ID."}, "expandView": {"message": "Expand view"}, "experimental": {"message": "Experimental"}, "exploreweb3": {"message": "Explore web3"}, "exportYourData": {"message": "Export your data"}, "exportYourDataButton": {"message": "Download"}, "exportYourDataDescription": {"message": "You can export data like your contacts and preferences."}, "extendWalletWithSnaps": {"message": "Explore community-built Snaps to customize your web3 experience", "description": "Banner description displayed on Snaps list page in Settings when less than 6 Snaps is installed."}, "externalAccount": {"message": "External Account"}, "externalExtension": {"message": "External extension"}, "externalNameSourcesSetting": {"message": "Proposed nicknames"}, "externalNameSourcesSettingDescription": {"message": "We’ll fetch proposed nicknames for addresses you interact with from third-party sources like Etherscan, Infura, and Lens Protocol. These sources will be able to see those addresses and your IP address. Your account address won’t be exposed to third parties."}, "failed": {"message": "Failed"}, "failedToFetchChainId": {"message": "Could not fetch chain ID. Is your RPC URL correct?"}, "failover": {"message": "Failover"}, "failoverRpcUrl": {"message": "Failover RPC URL"}, "failureMessage": {"message": "Something went wrong, and we were unable to complete the action"}, "fast": {"message": "Fast"}, "feeDetails": {"message": "Fee details"}, "fileImportFail": {"message": "File import not working? Click here!", "description": "Helps user import their account from a JSON file"}, "flaskWelcomeUninstall": {"message": "you should uninstall this extension", "description": "This request is shown on the Flask Welcome screen. It is intended for non-developers, and will be bolded."}, "flaskWelcomeWarning1": {"message": "Flask is for developers to experiment with new unstable APIs. Unless you are a developer or beta tester, $1.", "description": "This is a warning shown on the Flask Welcome screen, intended to encourage non-developers not to proceed any further. $1 is the bolded message 'flaskWelcomeUninstall'"}, "flaskWelcomeWarning2": {"message": "We do not guarantee the safety or stability of this extension. The new APIs offered by Flask are not hardened against phishing attacks, meaning that any site or snap that requires Flask might be a malicious attempt to steal your assets.", "description": "This explains the risks of using NeoNix Flask"}, "flaskWelcomeWarning3": {"message": "All Flask APIs are experimental. They may be changed or removed without notice, or they might stay on Flask indefinitely without ever being migrated to stable NeoNix. Use them at your own risk.", "description": "This message warns developers about unstable Flask APIs"}, "flaskWelcomeWarning4": {"message": "Make sure to disable your regular NeoNix extension when using Flask.", "description": "This message calls to pay attention about multiple versions of NeoNix running on the same site (Flask + Prod)"}, "flaskWelcomeWarningAcceptButton": {"message": "I accept the risks", "description": "this text is shown on a button, which the user presses to confirm they understand the risks of using Flask"}, "floatAmountToken": {"message": "Token amount must be an integer"}, "followUsOnTwitter": {"message": "Follow us on Twitter"}, "forbiddenIpfsGateway": {"message": "Forbidden IPFS Gateway: Please specify a CID gateway"}, "forgetDevice": {"message": "Forget this device"}, "forgotPassword": {"message": "Forgot password?"}, "forgotPasswordModalButton": {"message": "Reset wallet"}, "forgotPasswordModalDescription1": {"message": "NeoNix can’t recover your password for you."}, "forgotPasswordModalDescription2": {"message": "You can reset your wallet by entering the Secret Recovery Phrase you used when you set up your wallet."}, "forgotPasswordModalTitle": {"message": "Forgot your password?"}, "form": {"message": "form"}, "from": {"message": "From"}, "fromAddress": {"message": "From: $1", "description": "$1 is the address to include in the From label. It is typically shortened first using shortenAddress"}, "fromTokenLists": {"message": "From token lists: $1"}, "function": {"message": "Function: $1"}, "fundingMethod": {"message": "Funding method"}, "gas": {"message": "Gas"}, "gasDisplayAcknowledgeDappButtonText": {"message": "Edit suggested gas fee"}, "gasDisplayDappWarning": {"message": "This gas fee has been suggested by $1. Overriding this may cause a problem with your transaction. Please reach out to $1 if you have questions.", "description": "$1 represents the Dapp's origin"}, "gasFee": {"message": "Gas fee"}, "gasLimit": {"message": "Gas limit"}, "gasLimitRecommended": {"message": "Recommended gas limit is $1. If the gas limit is less than that, it may fail."}, "gasLimitTooLow": {"message": "Gas limit must be at least 21000"}, "gasLimitV2": {"message": "Gas limit"}, "gasOption": {"message": "Gas option"}, "gasPriceExcessive": {"message": "Your gas fee is set unnecessarily high. Consider lowering the amount."}, "gasPriceFetchFailed": {"message": "Gas price estimation failed due to network error."}, "gasTimingHoursShort": {"message": "$1 hrs", "description": "$1 represents a number of hours"}, "gasTimingLow": {"message": "Slow"}, "gasTimingMinutesShort": {"message": "$1 min", "description": "$1 represents a number of minutes"}, "gasTimingSecondsShort": {"message": "$1 sec", "description": "$1 represents a number of seconds"}, "gasUsed": {"message": "Gas used"}, "general": {"message": "General"}, "generalCameraError": {"message": "We couldn't access your camera. Please give it another try."}, "generalCameraErrorTitle": {"message": "Something went wrong...."}, "generalDescription": {"message": "Sync settings across devices, select network preferences, and track token data"}, "genericExplorerView": {"message": "View account on $1"}, "getTheNewestFeatures": {"message": "Get the newest features"}, "goToSite": {"message": "Go to site"}, "goerli": {"message": "Goerli test network"}, "gotIt": {"message": "Got it"}, "grantExactAccess": {"message": "Grant exact access"}, "gwei": {"message": "GWEI"}, "hardware": {"message": "Hardware"}, "hardwareWalletConnected": {"message": "Hardware wallet connected"}, "hardwareWalletLegacyDescription": {"message": "(legacy)", "description": "Text representing the MEW path"}, "hardwareWalletSubmissionWarningStep1": {"message": "Be sure your $1 is plugged in and to select the Ethereum app."}, "hardwareWalletSubmissionWarningStep2": {"message": "Enable \"smart contract data\" or \"blind signing\" on your $1 device."}, "hardwareWalletSubmissionWarningTitle": {"message": "Prior to clicking Submit:"}, "hardwareWalletSupportLinkConversion": {"message": "click here"}, "hardwareWallets": {"message": "Connect a hardware wallet"}, "hardwareWalletsInfo": {"message": "Hardware wallet integrations use API calls to external servers, which can see your IP address and the smart contract addresses you interact with."}, "hardwareWalletsMsg": {"message": "Select a hardware wallet you would like to use with NeoNix."}, "here": {"message": "here", "description": "as in -click here- for more information (goes with troubleTokenBalances)"}, "hexData": {"message": "Hex data"}, "hiddenAccounts": {"message": "Hidden accounts"}, "hide": {"message": "<PERSON>de"}, "hideAccount": {"message": "Hide account"}, "hideAdvancedDetails": {"message": "<PERSON>de advanced details"}, "hideSentitiveInfo": {"message": "Hide sensitive information"}, "hideTokenPrompt": {"message": "Hide token?"}, "hideTokenSymbol": {"message": "Hide $1", "description": "$1 is the symbol for a token (e.g. 'DAI')"}, "hideZeroBalanceTokens": {"message": "Hide tokens without balance"}, "high": {"message": "Aggressive"}, "highGasSettingToolTipMessage": {"message": "High probability, even in volatile markets. Use $1 to cover surges in network traffic due to things like popular NFT drops.", "description": "$1 is key 'high' (text: 'Aggressive') separated here so that it can be passed in with bold font-weight"}, "highLowercase": {"message": "high"}, "highestCurrentBid": {"message": "Highest current bid"}, "highestFloorPrice": {"message": "Highest floor price"}, "history": {"message": "History"}, "holdToRevealContent1": {"message": "Your Secret Recovery Phrase provides $1", "description": "$1 is a bolded text with the message from 'holdToRevealContent2'"}, "holdToRevealContent2": {"message": "full access to your wallet and funds.", "description": "Is the bolded text in 'holdToRevealContent1'"}, "holdToRevealContent3": {"message": "Do not share this with anyone. $1 $2", "description": "$1 is a message from 'holdToRevealContent4' and $2 is a text link with the message from 'holdToRevealContent5'"}, "holdToRevealContent4": {"message": "NeoNix Support will not request this,", "description": "Part of 'holdToRevealContent3'"}, "holdToRevealContent5": {"message": "but phishers might.", "description": "The text link in 'holdToRevealContent3'"}, "holdToRevealContentPrivateKey1": {"message": "Your Private Key provides $1", "description": "$1 is a bolded text with the message from 'holdToRevealContentPrivateKey2'"}, "holdToRevealContentPrivateKey2": {"message": "full access to your wallet and funds.", "description": "Is the bolded text in 'holdToRevealContentPrivateKey2'"}, "holdToRevealLockedLabel": {"message": "hold to reveal circle locked"}, "holdToRevealPrivateKey": {"message": "Hold to reveal Private Key"}, "holdToRevealPrivateKeyTitle": {"message": "Keep your private key safe"}, "holdToRevealSRP": {"message": "Hold to reveal SRP"}, "holdToRevealSRPTitle": {"message": "Keep your SRP safe"}, "holdToRevealUnlockedLabel": {"message": "hold to reveal circle unlocked"}, "honeypotDescription": {"message": "This token might pose a honeypot risk. It is advised to conduct due diligence before interacting to prevent any potential financial loss."}, "honeypotTitle": {"message": "Honey Pot"}, "howNetworkFeesWorkExplanation": {"message": "Estimated fee required to process the transaction. The max fee is $1."}, "howQuotesWork": {"message": "How quotes work"}, "howQuotesWorkExplanation": {"message": "This quote has the best return of the quotes we searched. This is based on the swap rate, which includes bridging fees and a $1% NeoNix fee, minus gas fees. Gas fees depend on how busy the network is and how complex the transaction is."}, "id": {"message": "ID"}, "ignoreAll": {"message": "Ignore all"}, "ignoreTokenWarning": {"message": "If you hide tokens, they will not be shown in your wallet. However, you can still add them by searching for them."}, "imToken": {"message": "imToken"}, "import": {"message": "Import", "description": "Button to import an account from a selected file"}, "importAWallet": {"message": "Import a wallet"}, "importAccountError": {"message": "Error importing account."}, "importAccountErrorIsSRP": {"message": "You have entered a Secret Recovery Phrase (or mnemonic). To import an account here, you have to enter a private key, which is a hexadecimal string of length 64."}, "importAccountErrorNotAValidPrivateKey": {"message": "This is not a valid private key. You have entered a hexadecimal string, but it must be 64 characters long."}, "importAccountErrorNotHexadecimal": {"message": "This is not a valid private key. You must enter a hexadecimal string of length 64."}, "importAccountJsonLoading1": {"message": "Expect this JSON import to take a few minutes and freeze NeoNix."}, "importAccountJsonLoading2": {"message": "We apologize, and we will make it faster in the future."}, "importAccountMsg": {"message": "Imported accounts won’t be associated with your NeoNix Secret Recovery Phrase. Learn more about imported accounts"}, "importNFT": {"message": "Import NFT"}, "importNFTAddressToolTip": {"message": "On OpenSea, for example, on the NFT's page under Details, there is a blue hyperlinked value labeled 'Contract Address'. If you click on this, it will take you to the contract's address on Etherscan; at the top-left of that page, there should be an icon labeled 'Contract', and to the right, a long string of letters and numbers. This is the address of the contract that created your NFT. Click on the 'copy' icon to the right of the address, and you'll have it on your clipboard."}, "importNFTPage": {"message": "Import NFT page"}, "importNFTTokenIdToolTip": {"message": "An NFT's ID is a unique identifier since no two NFTs are alike. Again, on OpenSea this number is under 'Details'. Make a note of it, or copy it onto your clipboard."}, "importNWordSRP": {"message": "I have a $1 word recovery phrase", "description": "$1 is the number of words in the recovery phrase"}, "importPrivateKey": {"message": "Private Key"}, "importSRPDescription": {"message": "Import an existing wallet with your 12 or 24-word secret recovery phrase."}, "importSRPNumberOfWordsError": {"message": "Secret Recovery Phrases contain 12, or 24 words"}, "importSRPWordError": {"message": "Word $1 is incorrect or misspelled.", "description": "$1 is the word that is incorrect or misspelled"}, "importSRPWordErrorAlternative": {"message": "Word $1 and $2 is incorrect or misspelled.", "description": "$1 and $2 are multiple words that are mispelled."}, "importSecretRecoveryPhrase": {"message": "Import Secret Recovery Phrase"}, "importSecretRecoveryPhraseUnknownError": {"message": "An unknown error occurred."}, "importSelectedTokens": {"message": "Import selected tokens?"}, "importSelectedTokensDescription": {"message": "Only the tokens you've selected will appear in your wallet. You can always import hidden tokens later by searching for them."}, "importTokenQuestion": {"message": "Import token?"}, "importTokenWarning": {"message": "Anyone can create a token with any name, including fake versions of existing tokens. Add and trade at your own risk!"}, "importTokensCamelCase": {"message": "Import tokens"}, "importTokensError": {"message": "We could not import the tokens. Please try again later."}, "importWallet": {"message": "Import wallet"}, "importWalletOrAccountHeader": {"message": "Import a wallet or account"}, "importWalletSuccess": {"message": "Secret Recovery Phrase $1 imported", "description": "$1 is the index of the secret recovery phrase"}, "importWithCount": {"message": "Import $1", "description": "$1 will the number of detected tokens that are selected for importing, if all of them are selected then $1 will be all"}, "imported": {"message": "Imported", "description": "status showing that an account has been fully loaded into the keyring"}, "inYourSettings": {"message": "in your Settings"}, "included": {"message": "included"}, "includesXTransactions": {"message": "Includes $1 transactions"}, "infuraBlockedNotification": {"message": "NeoNix is unable to connect to the blockchain host. Review possible reasons $1.", "description": "$1 is a clickable link with with text defined by the 'here' key"}, "initialTransactionConfirmed": {"message": "Your initial transaction was confirmed by the network. Click OK to go back."}, "insightsFromSnap": {"message": "Insights from $1", "description": "$1 represents the name of the snap"}, "install": {"message": "Install"}, "installOrigin": {"message": "Install origin"}, "installRequest": {"message": "Add to NeoNix"}, "installedOn": {"message": "Installed on $1", "description": "$1 is the date when the snap has been installed"}, "insufficientBalance": {"message": "Insufficient balance."}, "insufficientFunds": {"message": "Insufficient funds."}, "insufficientFundsForGas": {"message": "Insufficient funds for gas"}, "insufficientLockedLiquidityDescription": {"message": "The lack of adequately locked or burned liquidity leaves the token vulnerable to sudden liquidity withdrawals, potentially causing market instability."}, "insufficientLockedLiquidityTitle": {"message": "Insufficient Locked Liquidity"}, "insufficientTokens": {"message": "Insufficient tokens."}, "interactWithSmartContract": {"message": "Smart contract"}, "interactingWith": {"message": "Interacting with"}, "interactingWithTransactionDescription": {"message": "This is the contract you're interacting with. Protect yourself from scammers by verifying the details."}, "interaction": {"message": "Interaction"}, "invalidAddress": {"message": "Invalid address"}, "invalidAddressRecipient": {"message": "Recipient address is invalid"}, "invalidAssetType": {"message": "This asset is an NFT and needs to be re-added on the Import NFTs page found under the NFTs tab"}, "invalidChainIdTooBig": {"message": "Invalid chain ID. The chain ID is too big."}, "invalidCustomNetworkAlertContent1": {"message": "The chain ID for custom network '$1' has to be re-entered.", "description": "$1 is the name/identifier of the network."}, "invalidCustomNetworkAlertContent2": {"message": "To protect you from malicious or faulty network providers, chain IDs are now required for all custom networks."}, "invalidCustomNetworkAlertContent3": {"message": "Go to Settings > Network and enter the chain ID. You can find the chain IDs of most popular networks on $1.", "description": "$1 is a link to https://chainid.network"}, "invalidCustomNetworkAlertTitle": {"message": "Invalid custom network"}, "invalidHexData": {"message": "Invalid hex data"}, "invalidHexNumber": {"message": "Invalid hexadecimal number."}, "invalidHexNumberLeadingZeros": {"message": "Invalid hexadecimal number. Remove any leading zeros."}, "invalidIpfsGateway": {"message": "Invalid IPFS Gateway: The value must be a valid URL"}, "invalidNumber": {"message": "Invalid number. Enter a decimal or '0x'-prefixed hexadecimal number."}, "invalidNumberLeadingZeros": {"message": "Invalid number. Remove any leading zeros."}, "invalidRPC": {"message": "Invalid RPC URL"}, "invalidSeedPhrase": {"message": "Invalid Secret Recovery Phrase"}, "invalidSeedPhraseCaseSensitive": {"message": "Invalid input! Secret Recovery Phrase is case sensitive."}, "invalidSeedPhraseNotFound": {"message": "Secret Recovery Phrase not found."}, "ipfsGateway": {"message": "IPFS gateway"}, "ipfsGatewayDescription": {"message": "NeoNix uses third-party services to show images of your NFTs stored on IPFS, display information related to ENS addresses entered in your browser's address bar, and fetch icons for different tokens. Your IP address may be exposed to these services when you’re using them."}, "ipfsToggleModalDescriptionOne": {"message": "We use third-party services to show images of your NFTs stored on IPFS, display information related to ENS addresses entered in your browser's address bar, and fetch icons for different tokens. Your IP address may be exposed to these services when you’re using them."}, "ipfsToggleModalDescriptionTwo": {"message": "Selecting Confirm turns on IPFS resolution. You can turn it off in $1 at any time.", "description": "$1 is the method to turn off ipfs"}, "ipfsToggleModalSettings": {"message": "Settings > Security and privacy"}, "isSigningOrSubmitting": {"message": "A previous transaction is still being signed or submitted"}, "jazzAndBlockies": {"message": "Jazzicons and Blockies are two different styles of unique icons that help you identify an account at a glance."}, "jazzicons": {"message": "Jazzicons"}, "jsonFile": {"message": "JSON File", "description": "format for importing an account"}, "keyringAccountName": {"message": "Account name"}, "keyringAccountPublicAddress": {"message": "Public Address"}, "keyringSnapRemovalResult1": {"message": "$1 $2removed", "description": "Displays the result after removal of a keyring snap. $1 is the snap name, $2 is whether it is successful or not"}, "keyringSnapRemovalResultNotSuccessful": {"message": "not ", "description": "Displays the `not` word in $2."}, "keyringSnapRemoveConfirmation": {"message": "Type $1 to confirm you want to remove this snap:", "description": "Asks user to input the name nap prior to deleting the snap. $1 is the snap name"}, "keystone": {"message": "Keystone"}, "knownAddressRecipient": {"message": "Known contract address."}, "knownTokenWarning": {"message": "This action will edit tokens that are already listed in your wallet, which can be used to phish you. Only approve if you are certain that you mean to change what these tokens represent. Learn more about $1"}, "l1Fee": {"message": "L1 fee"}, "l1FeeTooltip": {"message": "L1 gas fee"}, "l2Fee": {"message": "L2 fee"}, "l2FeeTooltip": {"message": "L2 gas fee"}, "lastConnected": {"message": "Last connected"}, "lastSold": {"message": "Last sold"}, "lavaDomeCopyWarning": {"message": "For your safety, selecting this text is not available right now."}, "layer1Fees": {"message": "Layer 1 fees"}, "layer2Fees": {"message": "Layer 2 fees"}, "learnHow": {"message": "Learn how"}, "learnMore": {"message": "learn more"}, "learnMoreAboutGas": {"message": "Want to $1 about gas?", "description": "$1 will be replaced by the learnMore translation key"}, "learnMoreAboutPrivacy": {"message": "Learn more about privacy best practices."}, "learnMoreAboutSolanaAccounts": {"message": "Learn more about Solana accounts"}, "learnMoreKeystone": {"message": "Learn More"}, "learnMoreUpperCase": {"message": "Learn more"}, "learnMoreUpperCaseWithDot": {"message": "Learn more."}, "learnScamRisk": {"message": "scams and security risks."}, "leaveNeoNix": {"message": "Leave NeoNix Wallet?"}, "leaveNeoNixDesc": {"message": "You're about to visit a site outside of NeoNix Wallet. Double-check the URL before continuing."}, "ledgerAccountRestriction": {"message": "You need to make use your last account before you can add a new one."}, "ledgerConnectionInstructionCloseOtherApps": {"message": "Close any other software connected to your device and then click here to refresh."}, "ledgerConnectionInstructionHeader": {"message": "Prior to clicking confirm:"}, "ledgerConnectionInstructionStepFour": {"message": "Enable \"smart contract data\" or \"blind signing\" on your Ledger device."}, "ledgerConnectionInstructionStepThree": {"message": "Be sure your Ledger is plugged in and to select the Ethereum app."}, "ledgerDeviceOpenFailureMessage": {"message": "The Ledger device failed to open. Your Ledger might be connected to other software. Please close Ledger Live or other applications connected to your Ledger device, and try to connect again."}, "ledgerErrorConnectionIssue": {"message": "Reconnect your ledger, open the ETH app and try again."}, "ledgerErrorDevicedLocked": {"message": "Your Ledger is locked. Unlock it then try again."}, "ledgerErrorEthAppNotOpen": {"message": "To solve the issue, open the ETH application on your device and retry."}, "ledgerErrorTransactionDataNotPadded": {"message": "Ethereum transaction's input data isn't sufficiently padded."}, "ledgerLiveApp": {"message": "Ledger Live App"}, "ledgerLocked": {"message": "Cannot connect to Ledger device. Please make sure your device is unlocked and Ethereum app is opened."}, "ledgerMultipleDevicesUnsupportedInfoDescription": {"message": "To connect a new device, disconnect the previous one."}, "ledgerMultipleDevicesUnsupportedInfoTitle": {"message": "You can only connect one Ledger at a time"}, "ledgerTimeout": {"message": "Ledger Live is taking too long to respond or connection timeout. Make sure Ledger Live app is opened and your device is unlocked."}, "ledgerWebHIDNotConnectedErrorMessage": {"message": "The ledger device was not connected. If you wish to connect your Ledger, please click 'Continue' again and approve HID connection", "description": "An error message shown to the user during the hardware connect flow."}, "levelArrow": {"message": "level arrow"}, "lightTheme": {"message": "Light"}, "likeToImportToken": {"message": "Would you like to import this token?"}, "likeToImportTokens": {"message": "Would you like to import these tokens?"}, "lineaGoerli": {"message": "Linea Goerli test network"}, "lineaMainnet": {"message": "Linea Mainnet"}, "lineaSepolia": {"message": "Linea Sepolia test network"}, "link": {"message": "Link"}, "linkCentralizedExchanges": {"message": "Link your Coinbase or Binance accounts to transfer crypto to NeoNix Wallet for free."}, "links": {"message": "Links"}, "loadMore": {"message": "Load more"}, "loading": {"message": "Loading..."}, "loadingScreenSnapMessage": {"message": "Please complete the transaction on the Snap."}, "loadingTokenList": {"message": "Loading token list"}, "localhost": {"message": "Localhost 8545"}, "lock": {"message": "Lock"}, "lockNeoNix": {"message": "Lock NeoNix Wallet"}, "lockTimeInvalid": {"message": "Lock time must be a number between 0 and 10080"}, "logo": {"message": "$1 logo", "description": "$1 is the name of the ticker"}, "low": {"message": "Low"}, "lowEstimatedReturnTooltipMessage": {"message": "You’ll pay more than $1% of your starting amount in fees. Check your receiving amount and network fees."}, "lowEstimatedReturnTooltipTitle": {"message": "High cost"}, "lowGasSettingToolTipMessage": {"message": "Use $1 to wait for a cheaper price. Time estimates are much less accurate as prices are somewhat unpredictable.", "description": "$1 is key 'low' separated here so that it can be passed in with bold font-weight"}, "lowLowercase": {"message": "low"}, "mainnet": {"message": "Ethereum Mainnet"}, "mainnetToken": {"message": "This address matches a known Ethereum Mainnet token address. Recheck the contract address and network for the token you are trying to add."}, "makeAnotherSwap": {"message": "Create a new swap"}, "makeSureNoOneWatching": {"message": "Make sure nobody is looking", "description": "Warning to users to be care while creating and saving their new Secret Recovery Phrase"}, "manageDefaultSettings": {"message": "Manage default settings"}, "manageInstitutionalWallets": {"message": "Manage Institutional Wallets"}, "manageInstitutionalWalletsDescription": {"message": "Turn this on to enable institutional wallets."}, "manageNetworksMenuHeading": {"message": "Manage networks"}, "managePermissions": {"message": "Manage permissions"}, "marketCap": {"message": "Market cap"}, "marketDetails": {"message": "Market details"}, "max": {"message": "Max"}, "maxBaseFee": {"message": "Max base fee"}, "maxFee": {"message": "Max fee"}, "maxFeeTooltip": {"message": "A maximum fee provided to pay for the transaction."}, "maxPriorityFee": {"message": "Max priority fee"}, "medium": {"message": "Market"}, "mediumGasSettingToolTipMessage": {"message": "Use $1 for fast processing at current market price.", "description": "$1 is key 'medium' (text: 'Market') separated here so that it can be passed in with bold font-weight"}, "memo": {"message": "memo"}, "message": {"message": "Message"}, "NeoNixConnectStatusParagraphOne": {"message": "You now have more control over your account connections in NeoNix Wallet."}, "NeoNixConnectStatusParagraphThree": {"message": "Click it to manage your connected accounts."}, "NeoNixConnectStatusParagraphTwo": {"message": "The connection status button shows if the website you’re visiting is connected to your currently selected account."}, "metaMetricsIdNotAvailableError": {"message": "Since you've never opted into MetaMetrics, there's no data to delete here."}, "metadataModalSourceTooltip": {"message": "$1 is hosted on npm and $2 is this Snap’s unique identifier.", "description": "$1 is the snap name and $2 is the snap NPM id."}, "NeoNixNotificationsAreOff": {"message": "Wallet notifications are currently not active."}, "NeoNixSwapsOfflineDescription": {"message": "NeoNix Wallet Swaps is undergoing maintenance. Please check back later."}, "NeoNixVersion": {"message": "NeoNix Wallet Version"}, "methodData": {"message": "Method"}, "methodDataTransactionDesc": {"message": "Function executed based on decoded input data."}, "methodNotSupported": {"message": "Not supported with this account."}, "metrics": {"message": "Metrics"}, "millionAbbreviation": {"message": "M", "description": "Shortened form of 'million'"}, "mismatchedChainLinkText": {"message": "verify the network details", "description": "Serves as link text for the 'mismatched<PERSON><PERSON><PERSON>' key. This text will be embedded inside the translation for that key."}, "mismatchedChainRecommendation": {"message": "We recommend that you $1 before proceeding.", "description": "$1 is a clickable link with text defined by the 'mismatchedChainLinkText' key. The link will open to instructions for users to validate custom network details."}, "mismatchedNetworkName": {"message": "According to our record the network name may not correctly match this chain ID."}, "mismatchedNetworkSymbol": {"message": "The submitted currency symbol does not match what we expect for this chain ID."}, "mismatchedRpcChainId": {"message": "Chain ID returned by the custom network does not match the submitted chain ID."}, "mismatchedRpcUrl": {"message": "According to our records the submitted RPC URL value does not match a known provider for this chain ID."}, "missingSetting": {"message": "Can't find a setting?"}, "missingSettingRequest": {"message": "Request here"}, "more": {"message": "more"}, "moreAccounts": {"message": "+ $1 more accounts", "description": "$1 is the number of accounts"}, "moreNetworks": {"message": "+ $1 more networks", "description": "$1 is the number of networks"}, "moreQuotes": {"message": "More quotes"}, "multichainAddEthereumChainConfirmationDescription": {"message": "You're adding this network to NeoNix and giving this site permission to use it."}, "multichainQuoteCardBridgingLabel": {"message": "Bridging"}, "multichainQuoteCardQuoteLabel": {"message": "Quote"}, "multichainQuoteCardTimeLabel": {"message": "Time"}, "multipleSnapConnectionWarning": {"message": "$1 wants to use $2 Snaps", "description": "$1 is the dapp and $2 is the number of snaps it wants to connect to."}, "mustSelectOne": {"message": "Must select at least 1 token."}, "name": {"message": "Name"}, "nameAddressLabel": {"message": "Address", "description": "Label above address field in name component modal."}, "nameAlreadyInUse": {"message": "Name is already in use"}, "nameInstructionsNew": {"message": "If you know this address, give it a nickname to recognize it in the future.", "description": "Instruction text in name component modal when value is not recognised."}, "nameInstructionsRecognized": {"message": "This address has a default nickname, but you can edit it or explore other suggestions.", "description": "Instruction text in name component modal when value is recognized but not saved."}, "nameInstructionsSaved": {"message": "You've added a nickname for this address before. You can edit or view other suggested nicknames.", "description": "Instruction text in name component modal when value is saved."}, "nameLabel": {"message": "Nickname", "description": "Label above name input field in name component modal."}, "nameModalMaybeProposedName": {"message": "Maybe: $1", "description": "$1 is the proposed name"}, "nameModalTitleNew": {"message": "Unknown address", "description": "Title of the modal created by the name component when value is not recognised."}, "nameModalTitleRecognized": {"message": "Recognized address", "description": "Title of the modal created by the name component when value is recognized but not saved."}, "nameModalTitleSaved": {"message": "Saved address", "description": "Title of the modal created by the name component when value is saved."}, "nameProviderProposedBy": {"message": "Proposed by $1", "description": "$1 is the name of the provider"}, "nameProvider_ens": {"message": "Ethereum Name Service (ENS)"}, "nameProvider_etherscan": {"message": "Etherscan"}, "nameProvider_lens": {"message": "Lens Protocol"}, "nameProvider_token": {"message": "NeoNix Wallet"}, "nameSetPlaceholder": {"message": "Choose a nickname...", "description": "Placeholder text for name input field in name component modal."}, "nativeNetworkPermissionRequestDescription": {"message": "$1 is asking for your approval to:", "description": "$1 represents dapp name"}, "nativeTokenScamWarningConversion": {"message": "Edit network details"}, "nativeTokenScamWarningDescription": {"message": "The native token symbol does not match the expected symbol of the native token for the network with the associated chain ID. You have entered $1 while the expected token symbol is $2. Please verify you are connected to the correct chain.", "description": "$1 represents the currency name, $2 represents the expected currency symbol"}, "nativeTokenScamWarningDescriptionExpectedTokenFallback": {"message": "something else", "description": "graceful fallback for when token symbol isn't found"}, "nativeTokenScamWarningTitle": {"message": "Unexpected Native Token Symbol", "description": "Title for nativeTokenScamWarningDescription"}, "needHelp": {"message": "Need help? Contact $1", "description": "$1 represents `needHelpLinkText`, the text which goes in the help link"}, "needHelpFeedback": {"message": "Share your feedback"}, "needHelpLinkText": {"message": "NeoNix Wallet support"}, "needHelpSubmitTicket": {"message": "Submit a ticket"}, "needImportFile": {"message": "You must select a file to import.", "description": "User is important an account and needs to add a file to continue"}, "negativeETH": {"message": "Can not send negative amounts of ETH."}, "negativeOrZeroAmountToken": {"message": "Cannot send negative or zero amounts of asset."}, "network": {"message": "Network"}, "networkChanged": {"message": "Network changed"}, "networkChangedMessage": {"message": "You're now transacting on $1.", "description": "$1 is the name of the network"}, "networkDetails": {"message": "Network details"}, "networkFee": {"message": "Network fee"}, "networkIsBusy": {"message": "Network is busy. Gas prices are high and estimates are less accurate."}, "networkMenu": {"message": "Network Menu"}, "networkMenuHeading": {"message": "Select a network"}, "networkName": {"message": "Network name"}, "networkNameArbitrum": {"message": "Arbitrum"}, "networkNameAvalanche": {"message": "Avalanche"}, "networkNameBSC": {"message": "BSC"}, "networkNameBase": {"message": "Base"}, "networkNameBitcoin": {"message": "Bitcoin"}, "networkNameDefinition": {"message": "The name associated with this network."}, "networkNameEthereum": {"message": "Ethereum"}, "networkNameGoerli": {"message": "<PERSON><PERSON><PERSON>"}, "networkNameLinea": {"message": "Linea"}, "networkNameNeoNix": {"message": "NeoNix"}, "networkNameOpMainnet": {"message": "OP Mainnet"}, "networkNamePolygon": {"message": "Polygon"}, "networkNameSolana": {"message": "Solana"}, "networkNameTestnet": {"message": "Testnet"}, "networkNameZkSyncEra": {"message": "zkSync Era"}, "networkOptions": {"message": "Network options"}, "networkPermissionToast": {"message": "Network permissions updated"}, "networkProvider": {"message": "Network provider"}, "networkStatus": {"message": "Network status"}, "networkStatusBaseFeeTooltip": {"message": "The base fee is set by the network and changes every 13-14 seconds. Our $1 and $2 options account for sudden increases.", "description": "$1 and $2 are bold text for Medium and Aggressive respectively."}, "networkStatusPriorityFeeTooltip": {"message": "Range of priority fees (aka “miner tip”). This goes to miners and incentivizes them to prioritize your transaction."}, "networkStatusStabilityFeeTooltip": {"message": "Gas fees are $1 relative to the past 72 hours.", "description": "$1 is networks stability value - stable, low, high"}, "networkSwitchConnectionError": {"message": "We can't connect to $1", "description": "$1 represents the network name"}, "networkURL": {"message": "Network URL"}, "networkURLDefinition": {"message": "The URL used to access this network."}, "networkUrlErrorWarning": {"message": "Attackers sometimes mimic sites by making small changes to the site address. Make sure you're interacting with the intended site before you continue. Punycode version: $1", "description": "$1 replaced by RPC URL for network"}, "networks": {"message": "Networks"}, "networksSmallCase": {"message": "networks"}, "nevermind": {"message": "Nevermind"}, "new": {"message": "New!"}, "newAccount": {"message": "New account"}, "newAccountNumberName": {"message": "Account $1", "description": "Default name of next account to be created on create account screen"}, "newContact": {"message": "New contact"}, "newContract": {"message": "New contract"}, "newNFTDetectedInImportNFTsMessageStrongText": {"message": "Settings > Security and privacy"}, "newNFTDetectedInImportNFTsMsg": {"message": "To use Opensea to see your NFTs, turn on 'Display NFT Media' in $1.", "description": "$1 is used for newNFTDetectedInImportNFTsMessageStrongText"}, "newNFTDetectedInNFTsTabMessage": {"message": "Let NeoNix automatically detect and display NFTs in your wallet."}, "newNFTsAutodetected": {"message": "NFT autodetection"}, "newNetworkAdded": {"message": "“$1” was successfully added!"}, "newNetworkEdited": {"message": "“$1” was successfully edited!"}, "newNftAddedMessage": {"message": "NFT was successfully added!"}, "newPassword": {"message": "New password"}, "newPasswordPlaceholder": {"message": "Enter a strong password"}, "newPrivacyPolicyActionButton": {"message": "Read more"}, "newPrivacyPolicyTitle": {"message": "We’ve updated our privacy policy"}, "newRpcUrl": {"message": "New RPC URL"}, "newTokensImportedMessage": {"message": "You’ve successfully imported $1.", "description": "$1 is the string of symbols of all the tokens imported"}, "newTokensImportedTitle": {"message": "Token imported"}, "next": {"message": "Next"}, "nftAddFailedMessage": {"message": "NFT can’t be added as the ownership details do not match. Make sure you have entered correct information."}, "nftAddressError": {"message": "This token is an NFT. Add on the $1", "description": "$1 is a clickable link with text defined by the 'importNFTPage' key"}, "nftAlreadyAdded": {"message": "NFT has already been added."}, "nftAutoDetectionEnabled": {"message": "NFT autodetection enabled"}, "nftDisclaimer": {"message": "Disclaimer: NeoNix Wallet pulls the media file from the source url. This url sometimes gets changed by the marketplace on which the NFT was minted."}, "nftOptions": {"message": "NFT Options"}, "nftTokenIdPlaceholder": {"message": "Enter the token id"}, "nftWarningContent": {"message": "You're granting access to $1, including any you might own in the future. The party on the other end can transfer these NFTs from your wallet at any time without asking you until you revoke this approval. $2", "description": "$1 is nftWarningContentBold bold part, $2 is Learn more link"}, "nftWarningContentBold": {"message": "all your $1 NFTs", "description": "$1 is name of the collection"}, "nftWarningContentGrey": {"message": "Proceed with caution."}, "nfts": {"message": "NFTs"}, "nftsPreviouslyOwned": {"message": "Previously Owned"}, "nickname": {"message": "Nickname"}, "noAccountsFound": {"message": "No accounts found for the given search query"}, "noActivity": {"message": "No activity yet"}, "noConnectedAccountTitle": {"message": "NeoNix isn’t connected to this site"}, "noConnectionDescription": {"message": "To connect to a site, find and select the \"connect\" button. Remember NeoNix can only connect to sites on web3"}, "noConversionRateAvailable": {"message": "No conversion rate available"}, "noDeFiPositions": {"message": "No positions yet"}, "noDomainResolution": {"message": "No resolution for domain provided."}, "noHardwareWalletOrSnapsSupport": {"message": "Snaps, and most hardware wallets, will not work with your current browser version."}, "noNFTs": {"message": "No NFTs yet"}, "noNetworksFound": {"message": "No networks found for the given search query"}, "noOptionsAvailableMessage": {"message": "This trade route isn't available right now. Try changing the amount, network, or token and we'll find the best option."}, "noSnaps": {"message": "You don't have any snaps installed."}, "noThanks": {"message": "No thanks"}, "noTransactions": {"message": "You have no transactions"}, "noWebcamFound": {"message": "Your computer's webcam was not found. Please try again."}, "noWebcamFoundTitle": {"message": "Webcam not found"}, "nonContractAddressAlertDesc": {"message": "You're sending call data to an address that isn't a contract. This could cause you to lose funds. Make sure you're using the correct address and network before continuing."}, "nonContractAddressAlertTitle": {"message": "Potential mistake"}, "nonce": {"message": "<PERSON><PERSON>"}, "none": {"message": "None"}, "notBusy": {"message": "Not busy"}, "notCurrentAccount": {"message": "Is this the correct account? It's different from the currently selected account in your wallet"}, "notEnoughBalance": {"message": "Insufficient balance"}, "notEnoughGas": {"message": "Not enough gas"}, "notNow": {"message": "Not now"}, "notificationDetail": {"message": "Details"}, "notificationDetailBaseFee": {"message": "Base fee (GWEI)"}, "notificationDetailGasLimit": {"message": "Gas limit (units)"}, "notificationDetailGasUsed": {"message": "Gas used (units)"}, "notificationDetailMaxFee": {"message": "Max fee per gas"}, "notificationDetailNetwork": {"message": "Network"}, "notificationDetailNetworkFee": {"message": "Network fee"}, "notificationDetailPriorityFee": {"message": "Priority fee (GWEI)"}, "notificationItemCheckBlockExplorer": {"message": "Check on the Block Explorer"}, "notificationItemCollection": {"message": "Collection"}, "notificationItemConfirmed": {"message": "Confirmed"}, "notificationItemError": {"message": "Unable to retrieve fees currently"}, "notificationItemFrom": {"message": "From"}, "notificationItemLidoStakeReadyToBeWithdrawn": {"message": "<PERSON><PERSON><PERSON>"}, "notificationItemLidoStakeReadyToBeWithdrawnMessage": {"message": "You can now withdraw your unstaked $1"}, "notificationItemLidoWithdrawalRequestedMessage": {"message": "Your request to unstake $1 has been sent"}, "notificationItemNFTReceivedFrom": {"message": "Received NFT from"}, "notificationItemNFTSentTo": {"message": "Sent NFT to"}, "notificationItemNetwork": {"message": "Network"}, "notificationItemRate": {"message": "Rate (fee included)"}, "notificationItemReceived": {"message": "Received"}, "notificationItemReceivedFrom": {"message": "Received from"}, "notificationItemSent": {"message": "<PERSON><PERSON>"}, "notificationItemSentTo": {"message": "Sent to"}, "notificationItemStakeCompleted": {"message": "Stake completed"}, "notificationItemStaked": {"message": "Staked"}, "notificationItemStakingProvider": {"message": "Staking Provider"}, "notificationItemStatus": {"message": "Status"}, "notificationItemSwapped": {"message": "Swapped"}, "notificationItemSwappedFor": {"message": "for"}, "notificationItemTo": {"message": "To"}, "notificationItemTransactionId": {"message": "Transaction ID"}, "notificationItemUnStakeCompleted": {"message": "UnStaking complete"}, "notificationItemUnStaked": {"message": "Unstaked"}, "notificationItemUnStakingRequested": {"message": "Unstaking requested"}, "notificationTransactionFailedMessage": {"message": "Transaction $1 failed! $2", "description": "Content of the browser notification that appears when a transaction fails"}, "notificationTransactionFailedTitle": {"message": "Failed transaction", "description": "Title of the browser notification that appears when a transaction fails"}, "notificationTransactionSuccessMessage": {"message": "Transaction $1 confirmed!", "description": "Content of the browser notification that appears when a transaction is confirmed"}, "notificationTransactionSuccessTitle": {"message": "Confirmed transaction", "description": "Title of the browser notification that appears when a transaction is confirmed"}, "notificationTransactionSuccessView": {"message": "View on $1", "description": "Additional content in a notification that appears when a transaction is confirmed and has a block explorer URL."}, "notifications": {"message": "Notifications"}, "notificationsFeatureToggle": {"message": "Enable Wallet Notifications", "description": "Experimental feature title"}, "notificationsFeatureToggleDescription": {"message": "This enables wallet notifications like send/receive funds or nfts and feature announcements.", "description": "Description of the experimental notifications feature"}, "notificationsMarkAllAsRead": {"message": "Mark all as read"}, "notificationsPageEmptyTitle": {"message": "Nothing to see here"}, "notificationsPageErrorContent": {"message": "Please, try to visit this page again."}, "notificationsPageErrorTitle": {"message": "There has been an error"}, "notificationsPageNoNotificationsContent": {"message": "You have not received any notifications yet."}, "notificationsSettingsBoxError": {"message": "Something went wrong. Please try again."}, "notificationsSettingsPageAllowNotifications": {"message": "Stay in the loop on what’s happening in your wallet with notifications. To use notifications, we use a profile to sync some settings across your devices. $1"}, "notificationsSettingsPageAllowNotificationsLink": {"message": "Learn how we protect your privacy while using this feature."}, "numberOfNewTokensDetectedPlural": {"message": "$1 new tokens found in this account", "description": "$1 is the number of new tokens detected"}, "numberOfNewTokensDetectedSingular": {"message": "1 new token found in this account"}, "numberOfTokens": {"message": "Number of tokens"}, "ofTextNofM": {"message": "of"}, "off": {"message": "Off"}, "offlineForMaintenance": {"message": "Offline for maintenance"}, "ok": {"message": "Ok"}, "on": {"message": "On"}, "onboardedMetametricsAccept": {"message": "I agree"}, "onboardedMetametricsDisagree": {"message": "No thanks"}, "onboardedMetametricsKey1": {"message": "Latest developments"}, "onboardedMetametricsKey2": {"message": "Product features"}, "onboardedMetametricsKey3": {"message": "Other relevant promotional materials"}, "onboardedMetametricsLink": {"message": "MetaMetrics"}, "onboardedMetametricsParagraph1": {"message": "In addition to $1, we'd like to use data to understand how you interact with marketing communications.", "description": "$1 represents the 'onboardedMetametricsLink' locale string"}, "onboardedMetametricsParagraph2": {"message": "This helps us personalize what we share with you, like:"}, "onboardedMetametricsParagraph3": {"message": "Remember, we never sell the data you provide and you can opt out any time."}, "onboardedMetametricsTitle": {"message": "Help us enhance your experience"}, "onboardingAdvancedPrivacyIPFSDescription": {"message": "The IPFS gateway makes it possible to access and view data hosted by third parties. You can add a custom IPFS gateway or continue using the default."}, "onboardingAdvancedPrivacyIPFSInvalid": {"message": "Please enter a valid URL"}, "onboardingAdvancedPrivacyIPFSTitle": {"message": "Add custom IPFS Gateway"}, "onboardingAdvancedPrivacyIPFSValid": {"message": "IPFS gateway URL is valid"}, "onboardingAdvancedPrivacyNetworkDescription": {"message": "When you use our default settings and configurations, we use Infura as our default remote procedure call (RPC) provider to offer the most reliable and private access to Ethereum data we can. In limited cases, we may use other RPC providers in order to provide the best experience for our users. You can choose your own RPC, but remember that any RPC will receive your IP address and Ethereum wallet to make transactions. To learn more about how Infura handles data for EVM accounts, read our $1; for Solana accounts, $2."}, "onboardingAdvancedPrivacyNetworkDescriptionCallToAction": {"message": "click here"}, "onboardingAdvancedPrivacyNetworkTitle": {"message": "Choose your network"}, "onboardingContinueWith": {"message": "Continue with $1", "description": "$1 is the type of login used Google, Apple, etc."}, "onboardingCreateWallet": {"message": "Create a new wallet"}, "onboardingImportWallet": {"message": "I have an existing wallet"}, "onboardingMetametricsAgree": {"message": "I agree"}, "onboardingMetametricsDescription": {"message": "We'd like to gather basic usage and diagnostics data to improve NeoNix. It will always be:"}, "onboardingMetametricsInfuraTerms": {"message": "We’ll let you know if we plan to use this data for other purposes. You can review our $1 any time (we never sell the data you provide here).", "description": "$1 represents `onboardingMetametricsInfuraTermsPolicy`"}, "onboardingMetametricsInfuraTermsPolicy": {"message": "Privacy Policy"}, "onboardingMetametricsNeverCollect": {"message": "$1 Clicks and views on the app are stored, but other details (like your public address) are not.", "description": "$1 represents `onboardingMetametricsNeverCollectEmphasis`"}, "onboardingMetametricsNeverCollectEmphasis": {"message": "Private:"}, "onboardingMetametricsNeverCollectIP": {"message": "$1 We temporarily use your IP address to detect a general location (like your country or region), but it's never stored.", "description": "$1 represents `onboardingMetametricsNeverCollectIPEmphasis`"}, "onboardingMetametricsNeverCollectIPEmphasis": {"message": "General:"}, "onboardingMetametricsNeverSellData": {"message": "$1 You decide if you want to share or delete your usage data via settings any time.", "description": "$1 represents `onboardingMetametricsNeverSellDataEmphasis`"}, "onboardingMetametricsNeverSellDataEmphasis": {"message": "Optional:"}, "onboardingMetametricsTitle": {"message": "Help us improve NeoNix"}, "onboardingMetametricsUseDataCheckbox": {"message": "We’ll use this data to learn how you interact with our marketing communications. We may share relevant news (like product features)."}, "onboardingOptionIcon": {"message": "$1 icon", "description": "$1 is the icon name"}, "onboardingOptionTitle": {"message": "Choose an option to continue"}, "onboardingPinExtensionAltLaunch": {"message": "Launch extension"}, "onboardingPinExtensionAltPin": {"message": "Pin extension"}, "onboardingPinExtensionDescription": {"message": "Pin NeoNix on your browser so it’s accessible and easy to view transaction confirmations."}, "onboardingPinExtensionDescription2": {"message": "Access your NeoNix wallet with 1 click by clicking on the extension."}, "onboardingPinExtensionDescription3": {"message": "Click $1 extension icon to access it instantly", "description": "$1 is the browser name"}, "onboardingPinExtensionTitle": {"message": "Installation is complete!"}, "onboardingSignInWith": {"message": "Sign in with $1", "description": "$1 is the type of login used Google, Apple, etc"}, "onboardingSrpCreate": {"message": "Continue with <PERSON> Recovery Phrase"}, "onboardingSrpImport": {"message": "Import using Secret Recovery Phrase"}, "onboardingSrpImportError": {"message": "Use only lowercase letters, check your spelling, and put the words in the original order."}, "onboardingSrpInputClearAll": {"message": "Clear all"}, "onboardingSrpInputHideAll": {"message": "Hide all"}, "onboardingSrpInputPlaceholder": {"message": "Add a space between each word and make sure no one is watching."}, "onboardingSrpInputShowAll": {"message": "Show all"}, "onekey": {"message": "OneKey"}, "only": {"message": "only"}, "onlyConnectTrust": {"message": "Only connect with sites you trust. $1", "description": "Text displayed above the buttons for connection confirmation. $1 is the link to the learn more web page."}, "openFullScreenForLedgerWebHid": {"message": "Go to full screen to connect your Ledger.", "description": "Shown to the user on the confirm screen when they are viewing NeoNix in a popup window but need to connect their ledger via webhid."}, "openInBlockExplorer": {"message": "Open in block explorer"}, "optional": {"message": "Optional"}, "options": {"message": "Options"}, "or": {"message": "Or"}, "origin": {"message": "Origin"}, "originChanged": {"message": "Site changed"}, "originChangedMessage": {"message": "You're now reviewing a request from $1.", "description": "$1 is the name of the origin"}, "osTheme": {"message": "System"}, "other": {"message": "other"}, "otherSnaps": {"message": "other snaps", "description": "Used in the 'permission_rpc' message."}, "others": {"message": "others"}, "outdatedBrowserNotification": {"message": "Your browser is out of date. If you don't update your browser, you won't be able to get security patches and new features from NeoNix."}, "overrideContentSecurityPolicyHeader": {"message": "Override Content-Security-Policy header"}, "overrideContentSecurityPolicyHeaderDescription": {"message": "This option is a workaround for a known issue in Firefox, where a dapp's Content-Security-Policy header may prevent the extension from loading properly. Disabling this option is not recommended unless required for specific web page compatibility."}, "padlock": {"message": "Padlock"}, "participateInMetaMetrics": {"message": "Participate in MetaMetrics"}, "participateInMetaMetricsDescription": {"message": "Participate in MetaMetrics to help us make NeoNix better"}, "password": {"message": "Password"}, "passwordNotLongEnough": {"message": "Password must have at least 8 characters"}, "passwordStrength": {"message": "Password strength: $1", "description": "Return password strength to the user when user wants to create password."}, "passwordStrengthDescription": {"message": "A strong password can improve the security of your wallet should your device be stolen or compromised."}, "passwordTermsWarning": {"message": "NeoNix can’t recover this password."}, "passwordToggleHide": {"message": "Hide Password"}, "passwordToggleShow": {"message": "Show Password"}, "passwordsDontMatch": {"message": "Passwords don't match"}, "paste": {"message": "Paste"}, "pastePrivateKey": {"message": "Enter your private key string here:", "description": "For importing an account from a private key"}, "pending": {"message": "Pending"}, "pendingConfirmationAddNetworkAlertMessage": {"message": "Updating network will cancel $1 pending transactions from this site.", "description": "Number of transactions."}, "pendingConfirmationSwitchNetworkAlertMessage": {"message": "Switching network will cancel $1 pending transactions from this site.", "description": "Number of transactions."}, "pendingTransactionAlertMessage": {"message": "This transaction won't go through until a previous transaction is complete. $1", "description": "$1 represents the words 'how to cancel or speed up a transaction' in a hyperlink"}, "pendingTransactionAlertMessageHyperlink": {"message": "Learn how to cancel or speed up a transaction.", "description": "The text for the hyperlink in the pending transaction alert message"}, "permissionDetails": {"message": "Permission details"}, "permissionFor": {"message": "Permission for"}, "permissionFrom": {"message": "Permission from"}, "permissionRequested": {"message": "Requested now"}, "permissionRequestedForAccounts": {"message": "Requested now for $1", "description": "Permission cell status for requested permission including accounts, rendered as AvatarGroup which is $1."}, "permissionRevoked": {"message": "Revoked in this update"}, "permissionRevokedForAccounts": {"message": "Revoked in this update for $1", "description": "Permission cell status for revoked permission including accounts, rendered as AvatarGroup which is $1."}, "permission_accessNamedSnap": {"message": "Connect to $1.", "description": "The description for the `wallet_snap` permission. $1 is the human-readable name of the snap."}, "permission_accessNetwork": {"message": "Access the internet.", "description": "The description of the `endowment:network-access` permission."}, "permission_accessNetworkDescription": {"message": "Allow $1 to access the internet. This can be used to both send and receive data with third-party servers.", "description": "An extended description of the `endowment:network-access` permission. $1 is the snap name."}, "permission_accessSnap": {"message": "Connect to the $1 snap.", "description": "The description for the `wallet_snap` permission. $1 is the name of the snap."}, "permission_accessSnapDescription": {"message": "Allow the website or snap to interact with $1.", "description": "The description for the `wallet_snap_*` permission. $1 is the name of the Snap."}, "permission_assets": {"message": "Display account assets in NeoNix.", "description": "The description for the `endowment:assets` permission."}, "permission_assetsDescription": {"message": "Allow $1 to provide asset information to the NeoNix client. The assets can be onchain or offchain.", "description": "An extended description for the `endowment:assets` permission. $1 is the name of the Snap."}, "permission_cronjob": {"message": "Schedule and execute periodic actions.", "description": "The description for the `snap_cronjob` permission"}, "permission_cronjobDescription": {"message": "Allow $1 to perform actions that run periodically at fixed times, dates, or intervals. This can be used to trigger time-sensitive interactions or notifications.", "description": "An extended description for the `snap_cronjob` permission. $1 is the snap name."}, "permission_dialog": {"message": "Display dialog windows in NeoNix.", "description": "The description for the `snap_dialog` permission"}, "permission_dialogDescription": {"message": "Allow $1 to display NeoNix popups with custom text, input field, and buttons to approve or reject an action.\nCan be used to create e.g. alerts, confirmations, and opt-in flows for a snap.", "description": "An extended description for the `snap_dialog` permission. $1 is the snap name."}, "permission_ethereumAccounts": {"message": "See address, account balance, activity and suggest transactions to approve", "description": "The description for the `eth_accounts` permission"}, "permission_ethereumProvider": {"message": "Access the Ethereum provider.", "description": "The description for the `endowment:ethereum-provider` permission"}, "permission_ethereumProviderDescription": {"message": "Allow $1 to communicate with NeoNix directly, in order for it to read data from the blockchain and suggest messages and transactions.", "description": "An extended description for the `endowment:ethereum-provider` permission. $1 is the snap name."}, "permission_getEntropy": {"message": "Derive arbitrary keys unique to $1.", "description": "The description for the `snap_getEntropy` permission. $1 is the snap name."}, "permission_getEntropyDescription": {"message": "Allow $1 to derive arbitrary keys unique to $1, without exposing them. These keys are separate from your NeoNix account(s) and not related to your private keys or Secret Recovery Phrase. Other snaps cannot access this information.", "description": "An extended description for the `snap_getEntropy` permission. $1 is the snap name."}, "permission_getLocale": {"message": "View your preferred language.", "description": "The description for the `snap_getLocale` permission"}, "permission_getLocaleDescription": {"message": "Let $1 access your preferred language from your NeoNix settings. This can be used to localize and display $1's content using your language.", "description": "An extended description for the `snap_getLocale` permission. $1 is the snap name."}, "permission_getPreferences": {"message": "See information like your preferred language and fiat currency.", "description": "The description for the `snap_getPreferences` permission"}, "permission_getPreferencesDescription": {"message": "Let $1 access information like your preferred language and fiat currency in your NeoNix settings. This helps $1 display content tailored to your preferences. ", "description": "An extended description for the `snap_getPreferences` permission. $1 is the snap name."}, "permission_homePage": {"message": "Display a custom screen", "description": "The description for the `endowment:page-home` permission"}, "permission_homePageDescription": {"message": "Let $1 display a custom home screen in NeoNix. This can be used for user interfaces, configuration, and dashboards.", "description": "An extended description for the `endowment:page-home` permission. $1 is the snap name."}, "permission_keyring": {"message": "Allow requests for adding and controlling Ethereum accounts", "description": "The description for the `endowment:keyring` permission"}, "permission_keyringDescription": {"message": "Let $1 receive requests to add or remove accounts, plus sign and transact on behalf of these accounts.", "description": "An extended description for the `endowment:keyring` permission. $1 is the snap name."}, "permission_lifecycleHooks": {"message": "Use lifecycle hooks.", "description": "The description for the `endowment:lifecycle-hooks` permission"}, "permission_lifecycleHooksDescription": {"message": "Allow $1 to use lifecycle hooks to run code at specific times during its lifecycle.", "description": "An extended description for the `endowment:lifecycle-hooks` permission. $1 is the snap name."}, "permission_manageAccounts": {"message": "Add and control Ethereum accounts", "description": "The description for `snap_manageAccounts` permission"}, "permission_manageAccountsDescription": {"message": "Allow $1 to add or remove Ethereum accounts, then transact and sign with these accounts.", "description": "An extended description for the `snap_manageAccounts` permission. $1 is the snap name."}, "permission_manageBip32Keys": {"message": "Manage $1 accounts.", "description": "The description for the `snap_getBip32Entropy` permission. $1 is a derivation path, e.g. 'm/44'/0'/0' (secp256k1)'."}, "permission_manageBip44AndBip32KeysDescription": {"message": "Allow $1 to manage accounts and assets on the requested network. These accounts are derived and backed up using your secret recovery phrase (without revealing it). With the power to derive keys, $1 can support a variety of blockchain protocols beyond Ethereum (EVMs).", "description": "An extended description for the `snap_getBip44Entropy` and `snap_getBip44Entropy` permissions. $1 is the snap name."}, "permission_manageBip44Keys": {"message": "Manage $1 accounts.", "description": "The description for the `snap_getBip44Entropy` permission. $1 is the name of a protocol, e.g. 'Filecoin'."}, "permission_manageState": {"message": "Store and manage its data on your device.", "description": "The description for the `snap_manageState` permission"}, "permission_manageStateDescription": {"message": "Allow $1 to store, update, and retrieve data securely with encryption. Other snaps cannot access this information.", "description": "An extended description for the `snap_manageState` permission. $1 is the snap name."}, "permission_nameLookup": {"message": "Provide domain and address lookups.", "description": "The description for the `endowment:name-lookup` permission."}, "permission_nameLookupDescription": {"message": "Allow the snap to fetch and display address and domain lookups in different parts of the NeoNix UI.", "description": "An extended description for the `endowment:name-lookup` permission."}, "permission_notifications": {"message": "Show notifications.", "description": "The description for the `snap_notify` permission"}, "permission_notificationsDescription": {"message": "Allow $1 to display notifications within NeoNix. A short notification text can be triggered by a snap for actionable or time-sensitive information.", "description": "An extended description for the `snap_notify` permission. $1 is the snap name."}, "permission_protocol": {"message": "Provide protocol data for one or more chains.", "description": "The description for the `endowment:protocol` permission."}, "permission_protocolDescription": {"message": "Allow $1 to provide NeoNix with protocol data such as gas estimates or token information.", "description": "An extended description for the `endowment:protocol` permission. $1 is the name of the Snap."}, "permission_rpc": {"message": "Allow $1 to communicate directly with $2.", "description": "The description for the `endowment:rpc` permission. $1 is 'other snaps' or 'websites', $2 is the snap name."}, "permission_rpcDescription": {"message": "Allow $1 to send messages to $2 and receive a response from $2.", "description": "An extended description for the `endowment:rpc` permission. $1 is 'other snaps' or 'websites', $2 is the snap name."}, "permission_rpcDescriptionOriginList": {"message": "$1 and $2", "description": "A list of allowed origins where $2 is the last origin of the list and $1 is the rest of the list separated by ','."}, "permission_signatureInsight": {"message": "Display signature insights modal.", "description": "The description for the `endowment:signature-insight` permission"}, "permission_signatureInsightDescription": {"message": "Allow $1 to display a modal with insights on any signature request before approval. This can be used for anti-phishing and security solutions.", "description": "An extended description for the `endowment:signature-insight` permission. $1 is the snap name."}, "permission_signatureInsightOrigin": {"message": "See the origins of websites that initiate a signature request", "description": "The description for the `signatureOrigin` caveat, to be used with the `endowment:signature-insight` permission"}, "permission_signatureInsightOriginDescription": {"message": "Allow $1 to see the origin (URI) of websites that initiate signature requests. This can be used for anti-phishing and security solutions.", "description": "An extended description for the `signatureOrigin` caveat, to be used with the `endowment:signature-insight` permission. $1 is the snap name."}, "permission_transactionInsight": {"message": "Fetch and display transaction insights.", "description": "The description for the `endowment:transaction-insight` permission"}, "permission_transactionInsightDescription": {"message": "Allow $1 to decode transactions and show insights within the NeoNix UI. This can be used for anti-phishing and security solutions.", "description": "An extended description for the `endowment:transaction-insight` permission. $1 is the snap name."}, "permission_transactionInsightOrigin": {"message": "See the origins of websites that suggest transactions", "description": "The description for the `transactionOrigin` caveat, to be used with the `endowment:transaction-insight` permission"}, "permission_transactionInsightOriginDescription": {"message": "Allow $1 to see the origin (URI) of websites that suggest transactions. This can be used for anti-phishing and security solutions.", "description": "An extended description for the `transactionOrigin` caveat, to be used with the `endowment:transaction-insight` permission. $1 is the snap name."}, "permission_unknown": {"message": "Unknown permission: $1", "description": "$1 is the name of a requested permission that is not recognized."}, "permission_viewBip32PublicKeys": {"message": "View your public key for $1 ($2).", "description": "The description for the `snap_getBip32PublicKey` permission. $1 is a derivation path, e.g. 'm/44'/0'/0''. $2 is the elliptic curve name, e.g. 'secp256k1'."}, "permission_viewBip32PublicKeysDescription": {"message": "Allow $2 to view your public keys (and addresses) for $1. This does not grant any control of accounts or assets.", "description": "An extended description for the `snap_getBip32PublicKey` permission. $1 is a derivation path (name). $2 is the snap name."}, "permission_viewNamedBip32PublicKeys": {"message": "View your public key for $1.", "description": "The description for the `snap_getBip32PublicKey` permission. $1 is a name for the derivation path, e.g., 'Ethereum accounts'."}, "permission_walletSwitchEthereumChain": {"message": "Use your enabled networks", "description": "The label for the `wallet_switchEthereumChain` permission"}, "permission_webAssembly": {"message": "Support for WebAssembly.", "description": "The description of the `endowment:webassembly` permission."}, "permission_webAssemblyDescription": {"message": "Allow $1 to access low-level execution environments via WebAssembly.", "description": "An extended description of the `endowment:webassembly` permission. $1 is the snap name."}, "permissions": {"message": "Permissions"}, "permissionsPageEmptyContent": {"message": "Nothing to see here"}, "permissionsPageEmptySubContent": {"message": "This is where you can see the permissions you've given to installed Snaps or connected sites."}, "permitSimulationChange_approve": {"message": "Spending cap"}, "permitSimulationChange_bidding": {"message": "You bid"}, "permitSimulationChange_listing": {"message": "You list"}, "permitSimulationChange_nft_listing": {"message": "Listing price"}, "permitSimulationChange_receive": {"message": "You receive"}, "permitSimulationChange_revoke2": {"message": "Revoke"}, "permitSimulationChange_transfer": {"message": "You send"}, "permitSimulationDetailInfo": {"message": "You're giving the spender permission to spend this many tokens from your account."}, "permittedChainToastUpdate": {"message": "$1 has access to $2."}, "personalAddressDetected": {"message": "Personal address detected. Input the token contract address."}, "pinToTop": {"message": "Pin to top"}, "pleaseConfirm": {"message": "Please confirm"}, "plusMore": {"message": "+ $1 more", "description": "$1 is the number of additional items"}, "plusXMore": {"message": "+ $1 more", "description": "$1 is a number of additional but unshown items in a list- this message will be shown in place of those items"}, "popularNetworkAddToolTip": {"message": "Some of these networks rely on third parties. The connections may be less reliable or enable third-parties to track activity.", "description": "Learn more link"}, "popularNetworks": {"message": "Popular networks"}, "portfolio": {"message": "Portfolio"}, "preparingSwap": {"message": "Preparing swap..."}, "prev": {"message": "Prev"}, "price": {"message": "Price"}, "priceUnavailable": {"message": "price unavailable"}, "primaryType": {"message": "Primary type"}, "priorityFee": {"message": "Priority fee"}, "priorityFeeProperCase": {"message": "Priority Fee"}, "privacy": {"message": "Privacy"}, "privacyMsg": {"message": "Privacy policy"}, "privateKey": {"message": "Private Key", "description": "select this type of file to use to import an account"}, "privateKeyCopyWarning": {"message": "Private key for $1", "description": "$1 represents the account name"}, "privateKeyHidden": {"message": "The private key is hidden", "description": "Explains that the private key input is hidden"}, "privateKeyShow": {"message": "Show/Hide the private key input", "description": "Describes a toggle that is used to show or hide the private key input"}, "privateKeyShown": {"message": "This private key is being shown", "description": "Explains that the private key input is being shown"}, "privateKeyWarning": {"message": "Warning: Never disclose this key. Anyone with your private keys can steal any assets held in your account."}, "privateNetwork": {"message": "Private network"}, "proceedWithTransaction": {"message": "I want to proceed anyway"}, "productAnnouncements": {"message": "Product announcements"}, "proposedApprovalLimit": {"message": "Proposed approval limit"}, "provide": {"message": "Provide"}, "publicAddress": {"message": "Public address"}, "pushPlatformNotificationsFundsReceivedDescription": {"message": "You received $1 $2"}, "pushPlatformNotificationsFundsReceivedDescriptionDefault": {"message": "You received some tokens"}, "pushPlatformNotificationsFundsReceivedTitle": {"message": "Funds received"}, "pushPlatformNotificationsFundsSentDescription": {"message": "You successfully sent $1 $2"}, "pushPlatformNotificationsFundsSentDescriptionDefault": {"message": "You successfully sent some tokens"}, "pushPlatformNotificationsFundsSentTitle": {"message": "Funds sent"}, "pushPlatformNotificationsNftReceivedDescription": {"message": "You received new NFTs"}, "pushPlatformNotificationsNftReceivedTitle": {"message": "NFT received"}, "pushPlatformNotificationsNftSentDescription": {"message": "You have successfully sent an NFT"}, "pushPlatformNotificationsNftSentTitle": {"message": "NFT sent"}, "pushPlatformNotificationsStakingLidoStakeCompletedDescription": {"message": "Your Lido stake was successful"}, "pushPlatformNotificationsStakingLidoStakeCompletedTitle": {"message": "Stake complete"}, "pushPlatformNotificationsStakingLidoStakeReadyToBeWithdrawnDescription": {"message": "Your Lido stake is now ready to be withdrawn"}, "pushPlatformNotificationsStakingLidoStakeReadyToBeWithdrawnTitle": {"message": "Stake ready for withdrawal"}, "pushPlatformNotificationsStakingLidoWithdrawalCompletedDescription": {"message": "Your Lido withdrawal was successful"}, "pushPlatformNotificationsStakingLidoWithdrawalCompletedTitle": {"message": "Withdrawal completed"}, "pushPlatformNotificationsStakingLidoWithdrawalRequestedDescription": {"message": "Your Lido withdrawal request was submitted"}, "pushPlatformNotificationsStakingLidoWithdrawalRequestedTitle": {"message": "<PERSON><PERSON><PERSON> requested"}, "pushPlatformNotificationsStakingRocketpoolStakeCompletedDescription": {"message": "Your RocketPool stake was successful"}, "pushPlatformNotificationsStakingRocketpoolStakeCompletedTitle": {"message": "Stake complete"}, "pushPlatformNotificationsStakingRocketpoolUnstakeCompletedDescription": {"message": "Your RocketPool unstake was successful"}, "pushPlatformNotificationsStakingRocketpoolUnstakeCompletedTitle": {"message": "Unstake complete"}, "pushPlatformNotificationsSwapCompletedDescription": {"message": "Your NeoNix Swap was successful"}, "pushPlatformNotificationsSwapCompletedTitle": {"message": "Swap completed"}, "queued": {"message": "Queued"}, "quoteRate": {"message": "Quote rate"}, "quotedReceiveAmount": {"message": "$1 receive amount"}, "quotedTotalCost": {"message": "$1 total cost"}, "rank": {"message": "Rank"}, "rateIncludesMMFee": {"message": "Rate includes $1% fee"}, "reAddAccounts": {"message": "re-add any other accounts"}, "reAdded": {"message": "re-added"}, "readdToken": {"message": "You can add this token back in the future by going to “Import token” in your accounts options menu."}, "receive": {"message": "Receive"}, "receiveCrypto": {"message": "Receive crypto"}, "recipientAddressPlaceholderNew": {"message": "Enter public address (0x) or domain name"}, "recommendedGasLabel": {"message": "Recommended"}, "recoveryPhraseReminderBackupStart": {"message": "Start here"}, "recoveryPhraseReminderConfirm": {"message": "Got it"}, "recoveryPhraseReminderHasBackedUp": {"message": "Always keep your Secret Recovery Phrase in a secure and secret place"}, "recoveryPhraseReminderHasNotBackedUp": {"message": "Need to backup your Secret Recovery Phrase again?"}, "recoveryPhraseReminderItemOne": {"message": "Never share your Secret Recovery Phrase with anyone"}, "recoveryPhraseReminderItemTwo": {"message": "The NeoNix team will never ask for your Secret Recovery Phrase"}, "recoveryPhraseReminderSubText": {"message": "Your Secret Recovery Phrase controls all of your accounts."}, "recoveryPhraseReminderTitle": {"message": "Protect your funds"}, "redeposit": {"message": "Redeposit"}, "refreshList": {"message": "Refresh list"}, "reject": {"message": "Reject"}, "rejectAll": {"message": "Reject all"}, "rejectRequestsDescription": {"message": "You are about to batch reject $1 requests."}, "rejectRequestsN": {"message": "Reject $1 requests"}, "rejectTxsDescription": {"message": "You are about to batch reject $1 transactions."}, "rejectTxsN": {"message": "Reject $1 transactions"}, "rejected": {"message": "Rejected"}, "remove": {"message": "Remove"}, "removeAccount": {"message": "Remove account"}, "removeAccountDescription": {"message": "This account will be removed from your wallet. Please make sure you have the original Secret Recovery Phrase or private key for this imported account before continuing. You can import or create accounts again from the account drop-down. "}, "removeKeyringSnap": {"message": "Removing this Snap removes these accounts from NeoNix:"}, "removeKeyringSnapToolTip": {"message": "The snap controls the accounts, and by removing it, the accounts will be removed from NeoNix, too, but they will remain in the blockchain."}, "removeNFT": {"message": "Remove NFT"}, "removeNftErrorMessage": {"message": "We could not remove this NFT."}, "removeNftMessage": {"message": "NFT was successfully removed!"}, "removeSnap": {"message": "Remove <PERSON>"}, "removeSnapAccountDescription": {"message": "If you proceed, this account will no longer be available in NeoNix."}, "removeSnapAccountTitle": {"message": "Remove account"}, "removeSnapConfirmation": {"message": "Are you sure you want to remove $1?", "description": "$1 represents the name of the snap"}, "removeSnapDescription": {"message": "This action will delete the snap, its data and revoke your given permissions."}, "replace": {"message": "replace"}, "reportIssue": {"message": "Report an issue"}, "requestFrom": {"message": "Request from"}, "requestFromInfo": {"message": "This is the site asking for your signature."}, "requestFromInfoSnap": {"message": "This is the Snap asking for your signature."}, "requestFromTransactionDescription": {"message": "This is the site asking for your confirmation."}, "requestingFor": {"message": "Requesting for"}, "requestingForAccount": {"message": "Requesting for $1", "description": "Name of Account"}, "requestingForNetwork": {"message": "Requesting for $1", "description": "Name of Network"}, "required": {"message": "Required"}, "reset": {"message": "Reset"}, "resetWallet": {"message": "Reset wallet"}, "resetWalletSubHeader": {"message": "NeoNix does not keep a copy of your password. If you’re having trouble unlocking your account, you will need to reset your wallet. You can do this by providing the Secret Recovery Phrase you used when you set up your wallet."}, "resetWalletUsingSRP": {"message": "This action will delete your current wallet and Secret Recovery Phrase from this device, along with the list of accounts you’ve curated. After resetting with a Secret Recovery Phrase, you’ll see a list of accounts based on the Secret Recovery Phrase you use to reset. This new list will automatically include accounts that have a balance. You’ll also be able to $1 created previously. Custom accounts that you’ve imported will need to be $2, and any custom tokens you’ve added to an account will need to be $3 as well."}, "resetWalletWarning": {"message": "Make sure you’re using the correct Secret Recovery Phrase before proceeding. You will not be able to undo this."}, "restartNeoNix": {"message": "Restart <PERSON>"}, "restore": {"message": "Rest<PERSON>"}, "restoreUserData": {"message": "Restore user data"}, "resultPageError": {"message": "Error"}, "resultPageErrorDefaultMessage": {"message": "The operation failed."}, "resultPageSuccess": {"message": "Success"}, "resultPageSuccessDefaultMessage": {"message": "The operation completed successfully."}, "retryTransaction": {"message": "Retry transaction"}, "reusedTokenNameWarning": {"message": "A token here reuses a symbol from another token you watch, this can be confusing or deceptive."}, "revealSecretRecoveryPhrase": {"message": "Reveal Secret Recovery Phrase"}, "revealSeedWords": {"message": "Reveal Secret Recovery Phrase"}, "revealSeedWordsDescription1": {"message": "The $1 provides $2", "description": "This is a sentence consisting of link using 'revealSeedWordsSRPName' as $1 and bolded text using 'revealSeedWordsDescription3' as $2."}, "revealSeedWordsDescription2": {"message": "NeoNix is a $1. That means you're the owner of your SRP.", "description": "$1 is text link with the message from 'revealSeedWordsNonCustodialWallet'"}, "revealSeedWordsDescription3": {"message": "full access to your wallet and funds.\n"}, "revealSeedWordsNonCustodialWallet": {"message": "non-custodial wallet"}, "revealSeedWordsQR": {"message": "QR"}, "revealSeedWordsSRPName": {"message": "Secret Recovery Phrase (SRP)"}, "revealSeedWordsText": {"message": "Text"}, "revealSeedWordsWarning": {"message": "Make sure no one is looking at your screen. $1", "description": "$1 is bolded text using the message from 'revealSeedWordsWarning2'"}, "revealSeedWordsWarning2": {"message": "NeoNix Support will never request this.", "description": "The bolded texted in the second part of 'revealSeedWordsWarning'"}, "revealSensitiveContent": {"message": "Reveal sensitive content"}, "review": {"message": "Review"}, "reviewAlert": {"message": "Review alert"}, "reviewAlerts": {"message": "Review alerts"}, "reviewPendingTransactions": {"message": "Review pending transactions"}, "reviewPermissions": {"message": "Review permissions"}, "revokePermission": {"message": "Revoke permission"}, "revokePermissionTitle": {"message": "Remove $1 permission", "description": "The token symbol that is being revoked"}, "revokeSimulationDetailsDesc": {"message": "You're removing someone's permission to spend tokens from your account."}, "reward": {"message": "<PERSON><PERSON>"}, "rpcNameOptional": {"message": "RPC Name (Optional)"}, "rpcUrl": {"message": "RPC URL"}, "safeTransferFrom": {"message": "Safe transfer from"}, "save": {"message": "Save"}, "scanInstructions": {"message": "Place the QR code in front of your camera"}, "scanQrCode": {"message": "Scan QR code"}, "scrollDown": {"message": "Scroll down"}, "search": {"message": "Search"}, "searchAccounts": {"message": "Search accounts"}, "searchNfts": {"message": "Search NFTs"}, "searchTokens": {"message": "Search tokens"}, "searchTokensByNameOrAddress": {"message": "Search tokens by name or address"}, "secretRecoveryPhrase": {"message": "Secret Recovery Phrase"}, "secretRecoveryPhrasePlusNumber": {"message": "Secret Recovery Phrase $1", "description": "The $1 is the order of the Secret Recovery Phrase"}, "secureWallet": {"message": "Secure wallet"}, "secureWalletGetStartedButton": {"message": "Get started"}, "secureWalletRemindLaterButton": {"message": "Remind me later"}, "secureWalletWalletRecover": {"message": "It’s the only way to recover your wallet if you get locked out of the app or get a new device."}, "secureWalletWalletSaveSrp": {"message": "Don’t risk losing your funds. Protect your wallet by saving your $1 in a place you trust.", "description": "The $1 is the button text 'Secret Recovery Phrase'"}, "security": {"message": "Security"}, "securityAlert": {"message": "Security alert from $1 and $2"}, "securityAlerts": {"message": "Security alerts"}, "securityAlertsDescription": {"message": "This feature alerts you to malicious or unusual activity by actively reviewing transaction and signature requests. $1", "description": "Link to learn more about security alerts"}, "securityAndPrivacy": {"message": "Security & privacy"}, "securityDescription": {"message": "Reduce your chances of joining unsafe networks and protect your accounts"}, "securityMessageLinkForNetworks": {"message": "network scams and security risks"}, "securityProviderPoweredBy": {"message": "Powered by $1", "description": "The security provider that is providing data"}, "seeAllPermissions": {"message": "See all permissions", "description": "Used for revealing more content (e.g. permission list, etc.)"}, "seeDetails": {"message": "See details"}, "seedPhraseIntroTitle": {"message": "Secure your wallet"}, "seedPhraseReq": {"message": "Secret Recovery Phrases contain 12, 15, 18, 21, or 24 words"}, "seedPhraseReviewDetails": {"message": "This is your $1. Write it down in the correct order and keep it safe. If someone has your Secret Recovery Phrase, they can access your wallet. $2", "description": "The $1 is the bolded text 'Secret Recovery Phrase' and $2 is 'seedPhraseReviewDetails2'"}, "seedPhraseReviewDetails2": {"message": "Don’t share it with anyone, ever."}, "seedPhraseReviewTitle": {"message": "Save your Secret Recovery Phrase"}, "select": {"message": "Select"}, "selectAccountToConnect": {"message": "Select an account to connect"}, "selectAccounts": {"message": "Select the account(s) to use on this site"}, "selectAccountsForSnap": {"message": "Select the account(s) to use with this snap"}, "selectAll": {"message": "Select all"}, "selectAnAccount": {"message": "Select an account"}, "selectAnAccountAlreadyConnected": {"message": "This account has already been connected to NeoNix"}, "selectEnableDisplayMediaPrivacyPreference": {"message": "Turn on Display NFT Media"}, "selectHdPath": {"message": "Select HD path"}, "selectNFTPrivacyPreference": {"message": "Enable NFT Autodetection"}, "selectPathHelp": {"message": "If you don't see the accounts you expect, try switching the HD path or current selected network."}, "selectRpcUrl": {"message": "Select RPC URL"}, "selectSecretRecoveryPhrase": {"message": "Select Secret Recovery Phrase"}, "selectType": {"message": "Select Type"}, "selectedAccountMismatch": {"message": "Different account selected"}, "selectingAllWillAllow": {"message": "Selecting all will allow this site to view all of your current accounts. Make sure you trust this site."}, "send": {"message": "Send"}, "sendBugReport": {"message": "Send us a bug report."}, "sendNoContactsConversionText": {"message": "click here"}, "sendNoContactsDescription": {"message": "Contacts allow you to safely send transactions to another account multiple times.  To create a contact, $1", "description": "$1 represents the action text 'click here'"}, "sendNoContactsTitle": {"message": "You don't have any contacts yet"}, "sendSelectReceiveAsset": {"message": "Select asset to receive"}, "sendSelectSendAsset": {"message": "Select asset to send"}, "sendSpecifiedTokens": {"message": "Send $1", "description": "Symbol of the specified token"}, "sendSwapSubmissionWarning": {"message": "Clicking this button will immediately initiate your swap transaction. Please review your transaction details before proceeding."}, "sendTokenAsToken": {"message": "Send $1 as $2", "description": "Used in the transaction display list to describe a swap and send. $1 and $2 are the symbols of tokens in involved in the swap."}, "sendingAsset": {"message": "Sending $1"}, "sendingDisabled": {"message": "Sending of ERC-1155 NFT assets is not yet supported."}, "sendingNativeAsset": {"message": "Sending $1", "description": "$1 represents the native currency symbol for the current network (e.g. ETH or BNB)"}, "sendingToTokenContractWarning": {"message": "Warning: you are about to send to a token contract which could result in a loss of funds. $1", "description": "$1 is a clickable link with text defined by the 'learnMoreUpperCase' key. The link will open to a support article regarding the known contract address warning"}, "sepolia": {"message": "Sepolia test network"}, "setApprovalForAll": {"message": "Set approval for all"}, "setApprovalForAllRedesignedTitle": {"message": "<PERSON><PERSON><PERSON> request"}, "setApprovalForAllTitle": {"message": "Approve $1 with no spend limit", "description": "The token symbol that is being approved"}, "settingAddSnapAccount": {"message": "Add account Snap"}, "settings": {"message": "Settings"}, "settingsSearchMatchingNotFound": {"message": "No matching results found."}, "settingsSubHeadingSignaturesAndTransactions": {"message": "Signature and transaction requests"}, "show": {"message": "Show"}, "showAccount": {"message": "Show account"}, "showAdvancedDetails": {"message": "Show advanced details"}, "showExtensionInFullSizeView": {"message": "Show extension in full-size view"}, "showExtensionInFullSizeViewDescription": {"message": "Turn this on to make full-size view your default when you click the extension icon."}, "showFiatConversionInTestnets": {"message": "Show conversion on test networks"}, "showFiatConversionInTestnetsDescription": {"message": "Select this to show fiat conversion on test networks"}, "showHexData": {"message": "Show hex data"}, "showHexDataDescription": {"message": "Select this to show the hex data field on the send screen"}, "showLess": {"message": "Show less"}, "showMore": {"message": "Show more"}, "showNativeTokenAsMainBalance": {"message": "Show native token as main balance"}, "showNft": {"message": "Show NFT"}, "showPermissions": {"message": "Show permissions"}, "showPrivateKey": {"message": "Show private key"}, "showSRP": {"message": "Show Secret Recovery Phrase"}, "showTestnetNetworks": {"message": "Show test networks"}, "showTestnetNetworksDescription": {"message": "Select this to show test networks in network list"}, "sign": {"message": "Sign"}, "signatureRequest": {"message": "Signature request"}, "signature_decoding_bid_nft_tooltip": {"message": "The NFT will be reflected in your wallet, when the bid is accepted."}, "signature_decoding_list_nft_tooltip": {"message": "Expect changes only if someone buys your NFTs."}, "signed": {"message": "Signed"}, "signing": {"message": "Signing"}, "signingInWith": {"message": "Signing in with"}, "signingWith": {"message": "Signing with"}, "simulationApproveHeading": {"message": "Withdraw"}, "simulationDetailsApproveDesc": {"message": "You're giving someone else permission to withdraw NFTs from your account."}, "simulationDetailsERC20ApproveDesc": {"message": "You're giving someone else permission to spend this amount from your account."}, "simulationDetailsFiatNotAvailable": {"message": "Not Available"}, "simulationDetailsIncomingHeading": {"message": "You receive"}, "simulationDetailsNoChanges": {"message": "No changes"}, "simulationDetailsOutgoingHeading": {"message": "You send"}, "simulationDetailsRevokeSetApprovalForAllDesc": {"message": "You're removing someone else's permission to withdraw NFTs from your account."}, "simulationDetailsSetApprovalForAllDesc": {"message": "You're giving permission for someone else to withdraw NFTs from your account."}, "simulationDetailsTitle": {"message": "Estimated changes"}, "simulationDetailsTitleTooltip": {"message": "Estimated changes are what might happen if you go through with this transaction. This is just a prediction, not a guarantee."}, "simulationDetailsTotalFiat": {"message": "Total = $1", "description": "$1 is the total amount in fiat currency on one side of the transaction"}, "simulationDetailsTransactionReverted": {"message": "This transaction is likely to fail"}, "simulationDetailsUnavailable": {"message": "Unavailable"}, "simulationErrorMessageV2": {"message": "We were not able to estimate gas. There might be an error in the contract and this transaction may fail."}, "simulationsSettingDescription": {"message": "Turn this on to estimate balance changes of transactions and signatures before you confirm them. This doesn't guarantee their final outcome. $1"}, "simulationsSettingSubHeader": {"message": "Estimate balance changes"}, "singleNetwork": {"message": "1 network"}, "siweIssued": {"message": "Issued"}, "siweNetwork": {"message": "Network"}, "siweRequestId": {"message": "Request ID"}, "siweResources": {"message": "Resources"}, "siweURI": {"message": "URL"}, "skipAccountSecurity": {"message": "Skip account security?"}, "skipAccountSecurityDetails": {"message": "If you lose this Secret Recovery Phrase, you won’t be able to access this wallet."}, "skipAccountSecuritySecureNow": {"message": "Secure now"}, "skipAccountSecuritySkip": {"message": "<PERSON><PERSON>"}, "slideBridgeDescription": {"message": "Move across 9 chains, all within your wallet"}, "slideBridgeTitle": {"message": "Ready to bridge?"}, "slideCashOutDescription": {"message": "Sell your crypto for cash"}, "slideCashOutTitle": {"message": "Cash out with NeoNix"}, "slideDebitCardDescription": {"message": "Available in select regions"}, "slideDebitCardTitle": {"message": "NeoNix debit card"}, "slideFundWalletDescription": {"message": "Add or transfer tokens to get started"}, "slideFundWalletTitle": {"message": "Fund your wallet"}, "slideMultiSrpDescription": {"message": "Import and use multiple wallets in NeoNix"}, "slideMultiSrpTitle": {"message": "Add multiple Secret Recovery Phrases"}, "slideRemoteModeDescription": {"message": "Use your cold wallet wirelessly"}, "slideRemoteModeTitle": {"message": "Cold storage, fast access"}, "slideSmartAccountUpgradeDescription": {"message": "Same address, smarter features"}, "slideSmartAccountUpgradeTitle": {"message": "Start using smart accounts"}, "slideSolanaDescription": {"message": "Create a Solana account to get started"}, "slideSolanaTitle": {"message": "Solana is now supported"}, "slideSweepStakeDescription": {"message": "Mint an NFT now for a chance to win"}, "slideSweepStakeTitle": {"message": "Enter the $5000 USDC Giveaway!"}, "smartAccountAccept": {"message": "Use smart account"}, "smartAccountBetterTransaction": {"message": "Faster transactions, lower fees"}, "smartAccountBetterTransactionDescription": {"message": "Save time and money by processing transactions together."}, "smartAccountFeaturesDescription": {"message": "Keep the same account address, and you can switch back anytime."}, "smartAccountLabel": {"message": "Smart Account"}, "smartAccountPayToken": {"message": "Pay with any token, any time"}, "smartAccountPayTokenDescription": {"message": "Use the tokens you already have to cover network fees."}, "smartAccountReject": {"message": "Don’t use smart account"}, "smartAccountRequestFor": {"message": "Request for"}, "smartAccountSameAccount": {"message": "Same account, smarter features."}, "smartAccountSplashTitle": {"message": "Use smart account?"}, "smartAccountUpgradeBannerDescription": {"message": "Same address. Smarter features."}, "smartAccountUpgradeBannerTitle": {"message": "Switch to smart account"}, "smartContracts": {"message": "Smart contracts"}, "smartSwapsErrorNotEnoughFunds": {"message": "Not enough funds for a smart swap."}, "smartSwapsErrorUnavailable": {"message": "Smart Swaps are temporarily unavailable."}, "smartTransactionCancelled": {"message": "Your transaction was canceled"}, "smartTransactionCancelledDescription": {"message": "Your transaction couldn't be completed, so it was canceled to save you from paying unnecessary gas fees."}, "smartTransactionError": {"message": "Your transaction failed"}, "smartTransactionErrorDescription": {"message": "Sudden market changes can cause failures. If the problem continues, reach out to NeoNix customer support."}, "smartTransactionPending": {"message": "Your transaction was submitted"}, "smartTransactionSuccess": {"message": "Your transaction is complete"}, "smartTransactions": {"message": "Smart Transactions"}, "smartTransactionsEnabledDescription": {"message": " and MEV protection. Now on by default."}, "smartTransactionsEnabledLink": {"message": "Higher success rates"}, "smartTransactionsEnabledTitle": {"message": "Transactions just got smarter"}, "snapAccountCreated": {"message": "Account created"}, "snapAccountCreatedDescription": {"message": "Your new account is ready to use!"}, "snapAccountCreationFailed": {"message": "Account creation failed"}, "snapAccountCreationFailedDescription": {"message": "$1 didn't manage to create an account for you.", "description": "$1 is the snap name"}, "snapAccountRedirectFinishSigningTitle": {"message": "Finish signing"}, "snapAccountRedirectSiteDescription": {"message": "Follow the instructions from $1"}, "snapAccountRemovalFailed": {"message": "Account removal failed"}, "snapAccountRemovalFailedDescription": {"message": "$1 didn't manage to remove this account for you.", "description": "$1 is the snap name"}, "snapAccountRemoved": {"message": "Account removed"}, "snapAccountRemovedDescription": {"message": "This account will no longer be available to use in NeoNix."}, "snapAccounts": {"message": "Account Snaps"}, "snapAccountsDescription": {"message": "Accounts controlled by third-party Snaps."}, "snapConnectTo": {"message": "Connect to $1", "description": "$1 is the website URL or a Snap name. Used for Snaps pre-approved connections."}, "snapConnectionPermissionDescription": {"message": "Let $1 automatically connect to $2 without your approval.", "description": "Used for Snap pre-approved connections. $1 is the Snap name, $2 is a website URL."}, "snapConnectionWarning": {"message": "$1 wants to use $2", "description": "$2 is the snap and $1 is the dapp requesting connection to the snap."}, "snapDetailWebsite": {"message": "Website"}, "snapHomeMenu": {"message": "Snap Home Menu"}, "snapInstallRequest": {"message": "Installing $1 gives it the following permissions.", "description": "$1 is the snap name."}, "snapInstallSuccess": {"message": "Installation complete"}, "snapInstallWarningCheck": {"message": "$1 wants permission to do the following:", "description": "Warning message used in popup displayed on snap install. $1 is the snap name."}, "snapInstallWarningHeading": {"message": "Proceed with caution"}, "snapInstallWarningPermissionDescriptionForBip32View": {"message": "Allow $1 to view your public keys (and addresses). This does not grant any control of accounts or assets.", "description": "An extended description for the `snap_getBip32PublicKey` permission used for tooltip on Snap Install Warning screen (popup/modal). $1 is the snap name."}, "snapInstallWarningPermissionDescriptionForEntropy": {"message": "Allow $1 Snap to manage accounts and assets on the requested network(s). These accounts are derived and backed up using your secret recovery phrase (without revealing it). With the power to derive keys, $1 can support a variety of blockchain protocols beyond Ethereum (EVMs).", "description": "An extended description for the `snap_getBip44Entropy` and `snap_getBip44Entropy` permissions used for tooltip on Snap Install Warning screen (popup/modal). $1 is the snap name."}, "snapInstallWarningPermissionNameForEntropy": {"message": "Manage $1 accounts", "description": "Permission name used for the Permission Cell component displayed on warning popup when installing a Snap. $1 is list of account types."}, "snapInstallWarningPermissionNameForViewPublicKey": {"message": "View your public key for $1", "description": "Permission name used for the Permission Cell component displayed on warning popup when installing a Snap. $1 is list of account types."}, "snapInstallationErrorDescription": {"message": "$1 couldn’t be installed.", "description": "Error description used when snap installation fails. $1 is the snap name."}, "snapInstallationErrorTitle": {"message": "Installation failed", "description": "Error title used when snap installation fails."}, "snapResultError": {"message": "Error"}, "snapResultSuccess": {"message": "Success"}, "snapResultSuccessDescription": {"message": "$1 is ready to use"}, "snapUIAssetSelectorTitle": {"message": "Select an asset"}, "snapUpdateAlertDescription": {"message": "Get the latest version of $1", "description": "Description used in Snap update alert banner when snap update is available. $1 is the Snap name."}, "snapUpdateAvailable": {"message": "Update available"}, "snapUpdateErrorDescription": {"message": "$1 couldn’t be updated.", "description": "Error description used when snap update fails. $1 is the snap name."}, "snapUpdateErrorTitle": {"message": "Update failed", "description": "Error title used when snap update fails."}, "snapUpdateRequest": {"message": "Updating $1 gives it the following permissions.", "description": "$1 is the Snap name."}, "snapUpdateSuccess": {"message": "Update complete"}, "snapUrlIsBlocked": {"message": "This Snap wants to take you to a blocked site. $1."}, "snaps": {"message": "Snaps"}, "snapsConnected": {"message": "Snaps connected"}, "snapsNoInsight": {"message": "No insight to show"}, "snapsPrivacyWarningFirstMessage": {"message": "You acknowledge that any Snap that you install is a Third Party Service, unless otherwise identified, as defined in the Consensys $1. Your use of Third Party Services is governed by separate terms and conditions set forth by the Third Party Service provider. Consensys does not recommend the use of any Snap by any particular person for any particular reason. You access, rely upon or use the Third Party Service at your own risk. Consensys disclaims all responsibility and liability for any losses on account of your use of Third Party Services.", "description": "First part of a message in popup modal displayed when installing a snap for the first time. $1 is terms of use link."}, "snapsPrivacyWarningSecondMessage": {"message": "Any information you share with Third Party Services will be collected directly by those Third Party Services in accordance with their privacy policies. Please refer to their privacy policies for more information.", "description": "Second part of a message in popup modal displayed when installing a snap for the first time."}, "snapsPrivacyWarningThirdMessage": {"message": "Consensys has no access to information you share with Third Party Services.", "description": "Third part of a message in popup modal displayed when installing a snap for the first time."}, "snapsSettings": {"message": "Snap settings"}, "snapsTermsOfUse": {"message": "Terms of Use"}, "snapsToggle": {"message": "A snap will only run if it is enabled"}, "snapsUIError": {"message": "Contact the creators of $1 for further support.", "description": "This is shown when the insight snap throws an error. $1 is the snap name"}, "solanaAccountRequested": {"message": "This site is requesting a Solana account."}, "solanaAccountRequired": {"message": "A Solana account is required to connect to this site."}, "solanaImportAccounts": {"message": "Import Solana accounts"}, "solanaImportAccountsDescription": {"message": "Import a Secret Recovery Phrase to migrate your Solana account from another wallet."}, "solanaMoreFeaturesComingSoon": {"message": "More features coming soon"}, "solanaMoreFeaturesComingSoonDescription": {"message": "NFTs, hardware wallet support, and more coming soon."}, "solanaOnNeoNix": {"message": "<PERSON><PERSON> on NeoNix"}, "solanaSendReceiveSwapTokens": {"message": "Send, receive, and swap tokens"}, "solanaSendReceiveSwapTokensDescription": {"message": "Transfer and transact with tokens such as SOL, USDC, and more."}, "someNetworks": {"message": "$1 networks"}, "somethingDoesntLookRight": {"message": "Something doesn't look right? $1", "description": "A false positive message for users to contact support. $1 is a link to the support page."}, "somethingIsWrong": {"message": "Something's gone wrong. Try reloading the page."}, "somethingWentWrong": {"message": "We could not load this page."}, "sortBy": {"message": "Sort by"}, "sortByAlphabetically": {"message": "Alphabetically (A-Z)"}, "sortByDecliningBalance": {"message": "Declining balance ($1 high-low)", "description": "Indicates a descending order based on token fiat balance. $1 is the preferred currency symbol"}, "source": {"message": "Source"}, "spamModalBlockedDescription": {"message": "This site will be blocked for 1 minute."}, "spamModalBlockedTitle": {"message": "You've temporarily blocked this site"}, "spamModalDescription": {"message": "If you're being spammed with multiple requests, you can temporarily block the site."}, "spamModalTemporaryBlockButton": {"message": "Temporarily block this site"}, "spamModalTitle": {"message": "We've noticed multiple requests"}, "speed": {"message": "Speed"}, "speedUp": {"message": "Speed up"}, "speedUpCancellation": {"message": "Speed up this cancellation"}, "speedUpExplanation": {"message": "We’ve updated the gas fee based on current network conditions and have increased it by at least 10% (required by the network)."}, "speedUpPopoverTitle": {"message": "Speed up transaction"}, "speedUpTooltipText": {"message": "New gas fee"}, "speedUpTransaction": {"message": "Speed up this transaction"}, "spendLimitInsufficient": {"message": "Spend limit insufficient"}, "spendLimitInvalid": {"message": "Spend limit invalid; must be a positive number"}, "spendLimitPermission": {"message": "Spend limit permission"}, "spendLimitRequestedBy": {"message": "Spend limit requested by $1", "description": "Origin of the site requesting the spend limit"}, "spendLimitTooLarge": {"message": "Spend limit too large"}, "spender": {"message": "<PERSON>pender"}, "spenderTooltipDesc": {"message": "This is the address that will be able to withdraw your NFTs."}, "spenderTooltipERC20ApproveDesc": {"message": "This is the address that will be able to spend your tokens on your behalf."}, "spendingCap": {"message": "Spending cap"}, "spendingCaps": {"message": "Spending caps"}, "srpDesignImageAlt": {"message": "SRP vault image"}, "srpDetailsDescription": {"message": "A Secret Recovery Phrase, also called a seed phrase or mnemonic, is a set of words that lets you access and control your crypto wallet. To move your wallet to NeoNix, you need this phrase."}, "srpDetailsOwnsAccessListItemOne": {"message": "Take all your money"}, "srpDetailsOwnsAccessListItemThree": {"message": "Change your login information"}, "srpDetailsOwnsAccessListItemTwo": {"message": "Confirm transactions"}, "srpDetailsOwnsAccessListTitle": {"message": "Anyone with your Secret Recovery Phrase can:"}, "srpDetailsTitle": {"message": "What’s a Secret Recovery Phrase?"}, "srpInputNumberOfWords": {"message": "I have a $1-word phrase", "description": "This is the text for each option in the dropdown where a user selects how many words their secret recovery phrase has during import. The $1 is the number of words (either 12, 15, 18, 21, or 24)."}, "srpListName": {"message": "Secret Recovery Phrase $1", "description": "$1 is the order of the Secret Recovery Phrase"}, "srpListNumberOfAccounts": {"message": "$1 accounts", "description": "$1 is the number of accounts in the list"}, "srpListSelectionDescription": {"message": "The Secret Recovery Phrase your new account will be generated from"}, "srpListSingleOrZero": {"message": "$1 account", "description": "$1 is the number of accounts in the list, it is either 1 or 0"}, "srpPasteFailedTooManyWords": {"message": "Paste failed because it contained over 24 words. A secret recovery phrase can have a maximum of 24 words.", "description": "Description of SRP paste error when the pasted content has too many words"}, "srpPasteTip": {"message": "You can paste your entire secret recovery phrase into any field", "description": "Our secret recovery phrase input is split into one field per word. This message explains to users that they can paste their entire secrete recovery phrase into any field, and we will handle it correctly."}, "srpSecurityQuizGetStarted": {"message": "Get started"}, "srpSecurityQuizImgAlt": {"message": "An eye with a keyhole in the center, and three floating password fields"}, "srpSecurityQuizIntroduction": {"message": "To reveal your Secret Recovery Phrase, you need to correctly answer two questions"}, "srpSecurityQuizQuestionOneQuestion": {"message": "If you lose your Secret Recovery Phrase, NeoNix..."}, "srpSecurityQuizQuestionOneRightAnswer": {"message": "Can’t help you"}, "srpSecurityQuizQuestionOneRightAnswerDescription": {"message": "Write it down, engrave it on metal, or keep it in multiple secret spots so you never lose it. If you lose it, it’s gone forever."}, "srpSecurityQuizQuestionOneRightAnswerTitle": {"message": "Right! No one can help get your Secret Recovery Phrase back"}, "srpSecurityQuizQuestionOneWrongAnswer": {"message": "Can get it back for you"}, "srpSecurityQuizQuestionOneWrongAnswerDescription": {"message": "If you lose your Secret Recovery Phrase, it’s gone forever. No one can help you get it back, no matter what they might say."}, "srpSecurityQuizQuestionOneWrongAnswerTitle": {"message": "Wrong! No one can help get your Secret Recovery Phrase back"}, "srpSecurityQuizQuestionTwoQuestion": {"message": "If anyone, even a support agent, asks for your Secret Recovery Phrase..."}, "srpSecurityQuizQuestionTwoRightAnswer": {"message": "You’re being scammed"}, "srpSecurityQuizQuestionTwoRightAnswerDescription": {"message": "Anyone claiming to need your Secret Recovery Phrase is lying to you. If you share it with them, they will steal your assets."}, "srpSecurityQuizQuestionTwoRightAnswerTitle": {"message": "Correct! Sharing your Secret Recovery Phrase is never a good idea"}, "srpSecurityQuizQuestionTwoWrongAnswer": {"message": "You should give it to them"}, "srpSecurityQuizQuestionTwoWrongAnswerDescription": {"message": "Anyone claiming to need your Secret Recovery Phrase is lying to you. If you share it with them, they will steal your assets."}, "srpSecurityQuizQuestionTwoWrongAnswerTitle": {"message": "Nope! Never share your Secret Recovery Phrase with anyone, ever"}, "srpSecurityQuizTitle": {"message": "Security quiz"}, "srpToggleShow": {"message": "Show/Hide this word of the secret recovery phrase", "description": "Describes a toggle that is used to show or hide a single word of the secret recovery phrase"}, "srpWordHidden": {"message": "This word is hidden", "description": "Explains that a word in the secret recovery phrase is hidden"}, "srpWordShown": {"message": "This word is being shown", "description": "Explains that a word in the secret recovery phrase is being shown"}, "stable": {"message": "Stable"}, "stableLowercase": {"message": "stable"}, "stake": {"message": "Stake"}, "staked": {"message": "Staked"}, "standardAccountLabel": {"message": "Standard Account"}, "startEarning": {"message": "Start earning"}, "stateCorruptionAreYouSure": {"message": "Are you sure you want to proceed?"}, "stateCorruptionCopyAndRestoreBeforeRecovery": {"message": "You can try to copy and restore your state file manually before you decide to restore your vault by following $1.", "description": "$1 represents the `stateCorruptionTheseInstructions` localization key"}, "stateCorruptionCopyAndRestoreBeforeReset": {"message": "You can try to copy and restore your state file manually before you decide to reset NeoNix by following $1.", "description": "$1 represents the `stateCorruptionTheseInstructions` localization key"}, "stateCorruptionDetectedNoBackup": {"message": "Your vault cannot be automatically recovered."}, "stateCorruptionDetectedWithBackup": {"message": "Your vault can be recovered from an automated backup. Automatic recovery will delete your current settings and preferences, and restore only your vault."}, "stateCorruptionNeoNixDatabaseCannotBeAccessed": {"message": "Internal Error: Database cannot be accessed"}, "stateCorruptionResetNeoNixState": {"message": "Reset NeoNix State"}, "stateCorruptionResettingDatabase": {"message": "Resetting database…"}, "stateCorruptionRestoreAccountsFromBackup": {"message": "Restore Accounts"}, "stateCorruptionRestoringDatabase": {"message": "Restoring database…"}, "stateCorruptionTheseInstructions": {"message": "these instructions", "description": "This is a link to instructions on how to recover your Secret Recovery Phrase manually. It is used in the `stateCorruptionCopyAndRestoreBeforeRecovery` and `stateCorruptionCopyAndRestoreBeforeReset` localization keys."}, "stateCorruptionTheseInstructionsLinkTitle": {"message": "How to recover your Secret Recovery Phrase"}, "stateLogError": {"message": "Error in retrieving state logs."}, "stateLogFileName": {"message": "NeoNix state logs"}, "stateLogs": {"message": "State logs"}, "stateLogsDescription": {"message": "State logs contain your public account addresses and sent transactions."}, "status": {"message": "Status"}, "statusNotConnected": {"message": "Not connected"}, "statusNotConnectedAccount": {"message": "No accounts connected"}, "step1LatticeWallet": {"message": "Connect your Lattice1"}, "step1LatticeWalletMsg": {"message": "You can connect NeoNix Wallet to your Lattice1 device once it is set up and online. Unlock your device and have your Device ID ready.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "step1LedgerWallet": {"message": "Download Ledger app"}, "step1LedgerWalletMsg": {"message": "Download, set up, and enter your password to unlock $1.", "description": "$1 represents the `ledgerLiveApp` localization value"}, "step1TrezorWallet": {"message": "Connect your Trezor"}, "step1TrezorWalletMsg": {"message": "Plug your <PERSON>rez<PERSON> directly into your computer and unlock it. Make sure you use the correct passphrase.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "step2LedgerWallet": {"message": "Connect your Ledger"}, "step2LedgerWalletMsg": {"message": "Plug your Ledger directly into your computer, then  unlock it and open the Ethereum app.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "stepOf": {"message": "Step $1 of $2", "description": "$1 current step, $2 total steps"}, "stillGettingMessage": {"message": "Still getting this message?"}, "strong": {"message": "Strong"}, "stxCancelled": {"message": "<PERSON><PERSON><PERSON> would have failed"}, "stxCancelledDescription": {"message": "Your transaction would have failed and was cancelled to protect you from paying unnecessary gas fees."}, "stxCancelledSubDescription": {"message": "Try your swap again. We’ll be here to protect you against similar risks next time."}, "stxFailure": {"message": "Swap failed"}, "stxFailureDescription": {"message": "Sudden market changes can cause failures. If the problem persists, please reach out to $1.", "description": "This message is shown to a user if their swap fails. The $1 will be replaced by support.NeoNix.io"}, "stxOptInSupportedNetworksDescription": {"message": "Turn on Smart Transactions for more reliable and secure transactions on supported networks. $1"}, "stxPendingPrivatelySubmittingSwap": {"message": "Privately submitting your Swap..."}, "stxPendingPubliclySubmittingSwap": {"message": "Publicly submitting your Swap..."}, "stxSuccess": {"message": "Swap complete!"}, "stxSuccessDescription": {"message": "Your $1 is now available.", "description": "$1 is a token symbol, e.g. ETH"}, "stxSwapCompleteIn": {"message": "Swap will complete in <", "description": "'<' means 'less than', e.g. <PERSON><PERSON><PERSON> will complete in < 2:59"}, "stxTryingToCancel": {"message": "Trying to cancel your transaction..."}, "stxUnknown": {"message": "Status unknown"}, "stxUnknownDescription": {"message": "A transaction has been successful but we’re unsure what it is. This may be due to submitting another transaction while this swap was processing."}, "stxUserCancelled": {"message": "Swap cancelled"}, "stxUserCancelledDescription": {"message": "Your transaction has been cancelled and you did not pay any unnecessary gas fees."}, "submit": {"message": "Submit"}, "submitted": {"message": "Submitted"}, "suggestedBySnap": {"message": "Suggested by $1", "description": "$1 is the snap name"}, "suggestedCurrencySymbol": {"message": "Suggested currency symbol:"}, "suggestedTokenName": {"message": "Suggested name:"}, "supplied": {"message": "Supplied"}, "support": {"message": "Support"}, "supportCenter": {"message": "Visit our support center"}, "supportMultiRpcInformation": {"message": "We now support multiple RPCs for a single network. Your most recent RPC has been selected as the default one to resolve conflicting information."}, "surveyConversion": {"message": "Take our survey"}, "surveyTitle": {"message": "Shape the future of NeoNix"}, "swap": {"message": "<PERSON><PERSON><PERSON>"}, "swapAdjustSlippage": {"message": "Adjust slippage"}, "swapAggregator": {"message": "Aggregator"}, "swapAllowSwappingOf": {"message": "Allow swapping of $1", "description": "Shows a user that they need to allow a token for swapping on their hardware wallet"}, "swapAmountReceived": {"message": "Guaranteed amount"}, "swapAmountReceivedInfo": {"message": "This is the minimum amount you will receive. You may receive more depending on slippage."}, "swapAndSend": {"message": "Swap & Send"}, "swapAnyway": {"message": "Swap anyway"}, "swapApproval": {"message": "Approve $1 for swaps", "description": "Used in the transaction display list to describe a transaction that is an approve call on a token that is to be swapped.. $1 is the symbol of a token that has been approved."}, "swapApproveNeedMoreTokens": {"message": "You need $1 more $2 to complete this swap", "description": "Tells the user how many more of a given token they need for a specific swap. $1 is an amount of tokens and $2 is the token symbol."}, "swapAreYouStillThere": {"message": "Are you still there?"}, "swapAreYouStillThereDescription": {"message": "We’re ready to show you the latest quotes when you want to continue"}, "swapConfirmWithHwWallet": {"message": "Confirm with your hardware wallet"}, "swapContinueSwapping": {"message": "Continue swapping"}, "swapContractDataDisabledErrorDescription": {"message": "In the Ethereum app on your Ledger, go to \"Settings\" and allow contract data. Then, try your swap again."}, "swapContractDataDisabledErrorTitle": {"message": "Contract data is not enabled on your Ledger"}, "swapCustom": {"message": "custom"}, "swapDecentralizedExchange": {"message": "Decentralized exchange"}, "swapDirectContract": {"message": "Direct contract"}, "swapEditLimit": {"message": "Edit limit"}, "swapEnableDescription": {"message": "This is required and gives NeoNix permission to swap your $1.", "description": "Gives the user info about the required approval transaction for swaps. $1 will be the symbol of a token being approved for swaps."}, "swapEnableTokenForSwapping": {"message": "This will $1 for swapping", "description": "$1 is for the 'enableToken' key, e.g. 'enable ETH'"}, "swapEnterAmount": {"message": "Enter an amount"}, "swapEstimatedNetworkFees": {"message": "Estimated network fees"}, "swapEstimatedNetworkFeesInfo": {"message": "This is an estimate of the network fee that will be used to complete your swap. The actual amount may change according to network conditions."}, "swapFailedErrorDescriptionWithSupportLink": {"message": "Transaction failures happen and we are here to help. If this issue persists, you can reach our customer support at $1 for further assistance.", "description": "This message is shown to a user if their swap fails. The $1 will be replaced by support.NeoNix.io"}, "swapFailedErrorTitle": {"message": "Swap failed"}, "swapFetchingQuote": {"message": "Fetching quote"}, "swapFetchingQuoteNofN": {"message": "Fetching quote $1 of $2", "description": "A count of possible quotes shown to the user while they are waiting for quotes to be fetched. $1 is the number of quotes already loaded, and $2 is the total number of resources that we check for quotes. Keep in mind that not all resources will have a quote for a particular swap."}, "swapFetchingQuotes": {"message": "Fetching quotes..."}, "swapFetchingQuotesErrorDescription": {"message": "Hmmm... something went wrong. Try again, or if errors persist, contact customer support."}, "swapFetchingQuotesErrorTitle": {"message": "Error fetching quotes"}, "swapFromTo": {"message": "The swap of $1 to $2", "description": "Tells a user that they need to confirm on their hardware wallet a swap of 2 tokens. $1 is a source token and $2 is a destination token"}, "swapGasFeesDetails": {"message": "Gas fees are estimated and will fluctuate based on network traffic and transaction complexity."}, "swapGasFeesExplanation": {"message": "NeoNix doesn't make money from gas fees. These fees are estimates and can change based on how busy the network is and how complex a transaction is. Learn more $1.", "description": "$1 is a link (text in link can be found at 'swapGasFeesSummaryLinkText')"}, "swapGasFeesExplanationLinkText": {"message": "here", "description": "Text for link in swapGasFeesExplanation"}, "swapGasFeesLearnMore": {"message": "Learn more about gas fees"}, "swapGasFeesSplit": {"message": "Gas fees on the previous screen are split between these two transactions."}, "swapGasFeesSummary": {"message": "Gas fees are paid to crypto miners who process transactions on the $1 network. NeoNix does not profit from gas fees.", "description": "$1 is the selected network, e.g. Ethereum or BSC"}, "swapGasIncludedTooltipExplanation": {"message": "This quote incorporates gas fees by adjusting the token amount sent or received. You may receive ETH in a separate transaction on your activity list."}, "swapGasIncludedTooltipExplanationLinkText": {"message": "Learn more about gas fees"}, "swapHighSlippage": {"message": "High slippage"}, "swapIncludesGasAndNeoNixFee": {"message": "Includes gas and a $1% NeoNix fee", "description": "Provides information about the fee that NeoNix takes for swaps. $1 is a decimal number."}, "swapIncludesMMFee": {"message": "Includes a $1% NeoNix fee.", "description": "Provides information about the fee that NeoNix takes for swaps. $1 is a decimal number."}, "swapIncludesMMFeeAlt": {"message": "Quote reflects $1% NeoNix fee", "description": "Provides information about the fee that NeoNix takes for swaps using the latest copy. $1 is a decimal number."}, "swapIncludesNeoNixFeeViewAllQuotes": {"message": "Includes a $1% NeoNix fee – $2", "description": "Provides information about the fee that NeoNix takes for swaps. $1 is a decimal number and $2 is a link to view all quotes."}, "swapLearnMore": {"message": "Learn more about Swaps"}, "swapLiquiditySourceInfo": {"message": "We search multiple liquidity sources (exchanges, aggregators and professional market makers) to compare exchange rates and network fees."}, "swapLowSlippage": {"message": "Low slippage"}, "swapMaxSlippage": {"message": "Max slippage"}, "swapNeoNixFee": {"message": "NeoNix fee"}, "swapNeoNixFeeDescription": {"message": "The fee of $1% is automatically factored into this quote. You pay it in exchange for a license to use NeoNix's liquidity provider information aggregation software.", "description": "Provides information about the fee that NeoNix takes for swaps. $1 is a decimal number."}, "swapNQuotesWithDot": {"message": "$1 quotes.", "description": "$1 is the number of quotes that the user can select from when opening the list of quotes on the 'view quote' screen"}, "swapNewQuoteIn": {"message": "New quotes in $1", "description": "Tells the user the amount of time until the currently displayed quotes are update. $1 is a time that is counting down from 1:00 to 0:00"}, "swapNoTokensAvailable": {"message": "No tokens available matching $1", "description": "Tells the user that a given search string does not match any tokens in our token lists. $1 can be any string of text"}, "swapOnceTransactionHasProcess": {"message": "Your $1 will be added to your account once this transaction has processed.", "description": "This message communicates the token that is being transferred. It is shown on the awaiting swap screen. The $1 will be a token symbol."}, "swapPriceDifference": {"message": "You are about to swap $1 $2 (~$3) for $4 $5 (~$6).", "description": "This message represents the price slippage for the swap.  $1 and $4 are a number (ex: 2.89), $2 and $5 are symbols (ex: ETH), and $3 and $6 are fiat currency amounts."}, "swapPriceDifferenceTitle": {"message": "Price difference of ~$1%", "description": "$1 is a number (ex: 1.23) that represents the price difference."}, "swapPriceUnavailableDescription": {"message": "Price impact could not be determined due to lack of market price data. Please confirm that you are comfortable with the amount of tokens you are about to receive before swapping."}, "swapPriceUnavailableTitle": {"message": "Check your rate before proceeding"}, "swapProcessing": {"message": "Processing"}, "swapQuoteDetails": {"message": "Quote details"}, "swapQuoteNofM": {"message": "$1 of $2", "description": "A count of possible quotes shown to the user while they are waiting for quotes to be fetched. $1 is the number of quotes already loaded, and $2 is the total number of resources that we check for quotes. Keep in mind that not all resources will have a quote for a particular swap."}, "swapQuoteSource": {"message": "Quote source"}, "swapQuotesExpiredErrorDescription": {"message": "Please request new quotes to get the latest rates."}, "swapQuotesExpiredErrorTitle": {"message": "Quotes timeout"}, "swapQuotesNotAvailableDescription": {"message": "This trade route isn't available right now. Try changing the amount, network, or token and we'll find the best option."}, "swapQuotesNotAvailableErrorDescription": {"message": "Try adjusting the amount or slippage settings and try again."}, "swapQuotesNotAvailableErrorTitle": {"message": "No quotes available"}, "swapRate": {"message": "Rate"}, "swapReceiving": {"message": "Receiving"}, "swapReceivingInfoTooltip": {"message": "This is an estimate. The exact amount depends on slippage."}, "swapRequestForQuotation": {"message": "Request for quotation"}, "swapSelect": {"message": "Select"}, "swapSelectAQuote": {"message": "Select a quote"}, "swapSelectAToken": {"message": "Select token"}, "swapSelectQuotePopoverDescription": {"message": "Below are all the quotes gathered from multiple liquidity sources."}, "swapSelectToken": {"message": "Select token"}, "swapShowLatestQuotes": {"message": "Show latest quotes"}, "swapSlippageAutoDescription": {"message": "Auto"}, "swapSlippageHighDescription": {"message": "The slippage entered ($1%) is considered very high and may result in a bad rate", "description": "$1 is the amount of % for slippage"}, "swapSlippageHighTitle": {"message": "High slippage"}, "swapSlippageLowDescription": {"message": "A value this low ($1%) may result in a failed swap", "description": "$1 is the amount of % for slippage"}, "swapSlippageLowTitle": {"message": "Low slippage"}, "swapSlippageNegativeDescription": {"message": "Slippage must be greater or equal to zero"}, "swapSlippageNegativeTitle": {"message": "Increase slippage to continue"}, "swapSlippageOverLimitDescription": {"message": "Slippage tolerance must be 15% or less. Anything higher will result in a bad rate."}, "swapSlippageOverLimitTitle": {"message": "Very high slippage"}, "swapSlippagePercent": {"message": "$1%", "description": "$1 is the amount of % for slippage"}, "swapSlippageTooltip": {"message": "If the price changes between the time your order is placed and confirmed it’s called “slippage”. Your swap will automatically cancel if slippage exceeds your “slippage tolerance” setting."}, "swapSlippageZeroDescription": {"message": "There are fewer zero-slippage quote providers which will result in a less competitive quote."}, "swapSlippageZeroTitle": {"message": "Sourcing zero-slippage providers"}, "swapSource": {"message": "Liquidity source"}, "swapSuggested": {"message": "<PERSON><PERSON><PERSON> suggested"}, "swapSuggestedGasSettingToolTipMessage": {"message": "Swaps are complex and time sensitive transactions. We recommend this gas fee for a good balance between cost and confidence of a successful Swap."}, "swapSwapFrom": {"message": "Swap from"}, "swapSwapSwitch": {"message": "Switch token order"}, "swapSwapTo": {"message": "Swap to"}, "swapToConfirmWithHwWallet": {"message": "to confirm with your hardware wallet"}, "swapTokenAddedManuallyDescription": {"message": "Verify this token on $1 and make sure it is the token you want to trade.", "description": "$1 points the user to etherscan as a place they can verify information about a token. $1 is replaced with the translation for \"etherscan\""}, "swapTokenAddedManuallyTitle": {"message": "Token added manually"}, "swapTokenAvailable": {"message": "Your $1 has been added to your account.", "description": "This message is shown after a swap is successful and communicates the exact amount of tokens the user has received for a swap. The $1 is a decimal number of tokens followed by the token symbol."}, "swapTokenBalanceUnavailable": {"message": "We were unable to retrieve your $1 balance", "description": "This message communicates to the user that their balance of a given token is currently unavailable. $1 will be replaced by a token symbol"}, "swapTokenNotAvailable": {"message": "Token is not available to swap in this region"}, "swapTokenToToken": {"message": "Swap $1 to $2", "description": "Used in the transaction display list to describe a swap. $1 and $2 are the symbols of tokens in involved in a swap."}, "swapTokenVerifiedOn1SourceDescription": {"message": "$1 is only verified on 1 source. Consider verifying it on $2 before proceeding.", "description": "$1 is a token name, $2 points the user to etherscan as a place they can verify information about a token. $1 is replaced with the translation for \"etherscan\""}, "swapTokenVerifiedOn1SourceTitle": {"message": "Potentially inauthentic token"}, "swapTokenVerifiedSources": {"message": "Confirmed by $1 sources. Verify on $2.", "description": "$1 the number of sources that have verified the token, $2 points the user to a block explorer as a place they can verify information about the token."}, "swapTooManyDecimalsError": {"message": "$1 allows up to $2 decimals", "description": "$1 is a token symbol and $2 is the max. number of decimals allowed for the token"}, "swapTransactionComplete": {"message": "Transaction complete"}, "swapTwoTransactions": {"message": "2 transactions"}, "swapUnknown": {"message": "Unknown"}, "swapZeroSlippage": {"message": "0% Slippage"}, "swapsMaxSlippage": {"message": "Slippage tolerance"}, "swapsNotEnoughToken": {"message": "Not enough $1", "description": "Tells the user that they don't have enough of a token for a proposed swap. $1 is a token symbol"}, "swapsViewInActivity": {"message": "View in activity"}, "switch": {"message": "Switch"}, "switchEthereumChainConfirmationDescription": {"message": "This will switch the selected network within NeoNix to a previously added network:"}, "switchEthereumChainConfirmationTitle": {"message": "Allow this site to switch the network?"}, "switchInputCurrency": {"message": "Switch input currency"}, "switchNetwork": {"message": "Switch network"}, "switchNetworks": {"message": "Switch networks"}, "switchToNetwork": {"message": "Switch to $1", "description": "$1 represents the custom network that has previously been added"}, "switchToThisAccount": {"message": "Switch to this account"}, "switchedNetworkToastDecline": {"message": "Don't show again"}, "switchedNetworkToastMessage": {"message": "$1 is now active on $2", "description": "$1 represents the account name, $2 represents the network name"}, "switchedNetworkToastMessageNoOrigin": {"message": "You're now using $1", "description": "$1 represents the network name"}, "switchingNetworksCancelsPendingConfirmations": {"message": "Switching networks will cancel all pending confirmations"}, "symbol": {"message": "Symbol"}, "symbolBetweenZeroTwelve": {"message": "Symbol must be 11 characters or fewer."}, "tapToReveal": {"message": "Tap to reveal"}, "tapToRevealNote": {"message": "Make sure no one is watching your screen."}, "tenPercentIncreased": {"message": "10% increase"}, "terms": {"message": "Terms of use"}, "termsOfService": {"message": "Terms of service"}, "termsOfUseAgree": {"message": "Agree"}, "termsOfUseAgreeText": {"message": "I agree to the Terms of use, which apply to my use of NeoNix and all of its features"}, "termsOfUseFooterText": {"message": "Please scroll to read all sections"}, "termsOfUseTitle": {"message": "Review our Terms of Use"}, "testNetworks": {"message": "Test networks"}, "testnets": {"message": "Testnets"}, "theme": {"message": "Theme"}, "themeDescription": {"message": "Choose your preferred NeoNix theme."}, "thirdPartySoftware": {"message": "Third-party software notice", "description": "Title of a popup modal displayed when installing a snap for the first time."}, "time": {"message": "Time"}, "tipsForUsingAWallet": {"message": "Tips for using a wallet"}, "tipsForUsingAWalletDescription": {"message": "Adding tokens unlocks more ways to use web3."}, "to": {"message": "To"}, "toAddress": {"message": "To: $1", "description": "$1 is the address to include in the To label. It is typically shortened first using shortenAddress"}, "toggleDecodeDescription": {"message": "We use 4byte.directory and Sourcify services to decode and display more readable transaction data. This helps you understand the outcome of pending and past transactions, but can result in your IP address being shared."}, "token": {"message": "Token"}, "tokenAddress": {"message": "Token address"}, "tokenAlreadyAdded": {"message": "Token has already been added."}, "tokenAutoDetection": {"message": "Token autodetection"}, "tokenContractAddress": {"message": "Token contract address"}, "tokenDecimal": {"message": "Token decimal"}, "tokenDecimalFetchFailed": {"message": "Token decimal required. Find it on: $1"}, "tokenDetails": {"message": "Token details"}, "tokenFoundTitle": {"message": "1 new token found"}, "tokenId": {"message": "Token ID"}, "tokenList": {"message": "Token lists"}, "tokenMarketplace": {"message": "Token marketplace"}, "tokenScamSecurityRisk": {"message": "token scams and security risks"}, "tokenStandard": {"message": "Token standard"}, "tokenSymbol": {"message": "Token symbol"}, "tokens": {"message": "Tokens"}, "tokensFoundTitle": {"message": "$1 new tokens found", "description": "$1 is the number of new tokens detected"}, "tokensInCollection": {"message": "Tokens in collection"}, "tooltipApproveButton": {"message": "I understand"}, "tooltipSatusConnected": {"message": "connected"}, "tooltipSatusConnectedUpperCase": {"message": "Connected"}, "tooltipSatusNotConnected": {"message": "not connected"}, "total": {"message": "Total"}, "totalVolume": {"message": "Total volume"}, "transaction": {"message": "transaction"}, "transactionCancelAttempted": {"message": "Transaction cancel attempted with estimated gas fee of $1 at $2"}, "transactionCancelSuccess": {"message": "Transaction successfully cancelled at $2"}, "transactionConfirmed": {"message": "Transaction confirmed at $2."}, "transactionCreated": {"message": "Transaction created with a value of $1 at $2."}, "transactionDataFunction": {"message": "Function"}, "transactionDetailGasHeading": {"message": "Estimated gas fee"}, "transactionDetailMultiLayerTotalSubtitle": {"message": "Amount + fees"}, "transactionDropped": {"message": "Transaction dropped at $2."}, "transactionError": {"message": "Transaction error. Exception thrown in contract code."}, "transactionErrorNoContract": {"message": "Trying to call a function on a non-contract address."}, "transactionErrored": {"message": "Transaction encountered an error."}, "transactionFlowNetwork": {"message": "Network"}, "transactionHistoryBaseFee": {"message": "Base fee (GWEI)"}, "transactionHistoryL1GasLabel": {"message": "Total L1 gas fee"}, "transactionHistoryL2GasLimitLabel": {"message": "L2 gas limit"}, "transactionHistoryL2GasPriceLabel": {"message": "L2 gas price"}, "transactionHistoryMaxFeePerGas": {"message": "Max fee per gas"}, "transactionHistoryPriorityFee": {"message": "Priority fee (GWEI)"}, "transactionHistoryTotalGasFee": {"message": "Total gas fee"}, "transactionIdLabel": {"message": "Transaction ID", "description": "Label for the source transaction ID field."}, "transactionIncludesTypes": {"message": "This transaction includes: $1."}, "transactionResubmitted": {"message": "Transaction resubmitted with estimated gas fee increased to $1 at $2"}, "transactionSettings": {"message": "Transaction settings"}, "transactionSubmitted": {"message": "Transaction submitted with estimated gas fee of $1 at $2."}, "transactionTotalGasFee": {"message": "Total gas fee", "description": "Label for the total gas fee incurred in the transaction."}, "transactionUpdated": {"message": "Transaction updated at $2."}, "transactions": {"message": "Transactions"}, "transfer": {"message": "Transfer"}, "transferCrypto": {"message": "Transfer crypto"}, "transferFrom": {"message": "Transfer from"}, "transferRequest": {"message": "Transfer request"}, "trillionAbbreviation": {"message": "T", "description": "Shortened form of 'trillion'"}, "troubleConnectingToLedgerU2FOnFirefox": {"message": "We're having trouble connecting your Ledger. $1", "description": "$1 is a link to the wallet connection guide;"}, "troubleConnectingToLedgerU2FOnFirefox2": {"message": "Review our hardware wallet connection guide and try again.", "description": "$1 of the ledger wallet connection guide"}, "troubleConnectingToLedgerU2FOnFirefoxLedgerSolution": {"message": "If you're on the latest version of Firefox, you might be experiencing an issue related to Firefox dropping U2F support. Learn how to fix this issue $1.", "description": "It is a link to the ledger website for the workaround."}, "troubleConnectingToLedgerU2FOnFirefoxLedgerSolution2": {"message": "here", "description": "Second part of the error message; It is a link to the ledger website for the workaround."}, "troubleConnectingToWallet": {"message": "We had trouble connecting to your $1, try reviewing $2 and try again.", "description": "$1 is the wallet device name; $2 is a link to wallet connection guide"}, "troubleStarting": {"message": "NeoNix Wallet had trouble starting. This error could be intermittent, so try restarting the extension."}, "tryAgain": {"message": "Try again"}, "turnOff": {"message": "Turn off"}, "turnOffNeoNixNotificationsError": {"message": "There was an error in disabling the notifications. Please try again later."}, "turnOn": {"message": "Turn on"}, "turnOnNeoNixNotifications": {"message": "Turn on notifications"}, "turnOnNeoNixNotificationsButton": {"message": "Turn on"}, "turnOnNeoNixNotificationsError": {"message": "There was an error in creating the notifications. Please try again later."}, "turnOnNeoNixNotificationsMessageFirst": {"message": "Stay in the loop on what's happening in your wallet with notifications."}, "turnOnNeoNixNotificationsMessagePrivacyBold": {"message": "notifications settings."}, "turnOnNeoNixNotificationsMessagePrivacyLink": {"message": "Learn how we protect your privacy while using this feature."}, "turnOnNeoNixNotificationsMessageSecond": {"message": "To use wallet notifications, we use a profile to sync some settings across your devices. $1"}, "turnOnNeoNixNotificationsMessageThird": {"message": "You can turn off notifications at any time in the $1"}, "turnOnTokenDetection": {"message": "Turn on enhanced token detection"}, "tutorial": {"message": "Tutorial"}, "twelveHrTitle": {"message": "12hr:"}, "typeYourSRP": {"message": "Enter your Secret Recovery Phrase"}, "u2f": {"message": "U2F", "description": "A name on an API for the browser to interact with devices that support the U2F protocol. On some browsers we use it to connect NeoNix to Ledger devices."}, "unapproved": {"message": "Unapproved"}, "unexpectedBehavior": {"message": "This behavior is unexpected and should be reported as a bug, even if your accounts are restored."}, "units": {"message": "units"}, "unknown": {"message": "Unknown"}, "unknownCollection": {"message": "Unnamed collection"}, "unknownNetworkForKeyEntropy": {"message": "Unknown network", "description": "Displayed on places like Snap install warning when regular name is not available."}, "unknownQrCode": {"message": "Error: We couldn't identify that QR code"}, "unlimited": {"message": "Unlimited"}, "unlock": {"message": "Unlock"}, "unpin": {"message": "Unpin"}, "unrecognizedChain": {"message": "This custom network is not recognized", "description": "$1 is a clickable link with text defined by the 'unrecognizedChanLinkText' key. The link will open to instructions for users to validate custom network details."}, "unsendableAsset": {"message": "Sending NFT (ERC-721) tokens is not currently supported", "description": "This is an error message we show the user if they attempt to send an NFT asset type, for which currently don't support sending"}, "unstableTokenPriceDescription": {"message": "The price of this token in USD is highly volatile, indicating a high risk of losing significant value by interacting with it."}, "unstableTokenPriceTitle": {"message": "Unstable <PERSON><PERSON>"}, "upArrow": {"message": "up arrow"}, "update": {"message": "Update"}, "updateEthereumChainConfirmationDescription": {"message": "This site is requesting to update your default network URL. You can edit defaults and network information any time."}, "updateInformation": {"message": "We've made your wallet safer, smoother, and added some new features. Update now to stay protected and use our latest improvements."}, "updateNetworkConfirmationTitle": {"message": "Update $1", "description": "$1 represents network name"}, "updateOrEditNetworkInformations": {"message": "Update your information or"}, "updateRequest": {"message": "Update request"}, "updateToTheLatestVersion": {"message": "Update to the latest version"}, "updatedRpcForNetworks": {"message": "Network RPCs Updated"}, "uploadDropFile": {"message": "Drop your file here"}, "uploadFile": {"message": "Upload file"}, "urlErrorMsg": {"message": "URLs require the appropriate HTTP/HTTPS prefix."}, "use4ByteResolution": {"message": "Decode smart contracts"}, "useMultiAccountBalanceChecker": {"message": "Batch account balance requests"}, "useMultiAccountBalanceCheckerSettingDescription": {"message": "Get faster balance updates by batching account balance requests. This lets us fetch your account balances together, so you get quicker updates for an improved experience. When this feature is off, third parties may be less likely to associate your accounts with each other."}, "useNftDetection": {"message": "Autodetect NFTs"}, "useNftDetectionDescriptionText": {"message": "Let NeoNix Wallet add NFTs you own using third-party services. Autodetecting NFTs exposes your IP and account address to these services. Enabling this feature could associate your IP address with your Ethereum address and display fake NFTs airdropped by scammers. You can add tokens manually to avoid this risk."}, "usePhishingDetection": {"message": "Use phishing detection"}, "usePhishingDetectionDescription": {"message": "Display a warning for phishing domains targeting Ethereum users"}, "useSafeChainsListValidation": {"message": "Network details check"}, "useSafeChainsListValidationDescription": {"message": "NeoNix uses a third-party service called $1 to show accurate and standardized network details. This reduces your chances of connecting to malicious or incorrect network. When using this feature, your IP address is exposed to chainid.network."}, "useSafeChainsListValidationWebsite": {"message": "chainid.network", "description": "useSafeChainsListValidationWebsite is separated from the rest of the text so that we can bold the third party service name in the middle of them"}, "useTokenDetectionPrivacyDesc": {"message": "Automatically displaying tokens sent to your account involves communication with third party servers to fetch token’s images. Those serves will have access to your IP address."}, "usedByClients": {"message": "Used by a variety of different clients"}, "userName": {"message": "Username"}, "userOpContractDeployError": {"message": "Contract deployment from a smart contract account is not supported"}, "version": {"message": "Version"}, "view": {"message": "View"}, "viewActivity": {"message": "View activity"}, "viewAllQuotes": {"message": "view all quotes"}, "viewContact": {"message": "View contact"}, "viewDetails": {"message": "View details"}, "viewMore": {"message": "View more"}, "viewOnBlockExplorer": {"message": "View on block explorer"}, "viewOnCustomBlockExplorer": {"message": "View $1 at $2", "description": "$1 is the action type. e.g (Account, Transaction, Swap) and $2 is the Custom Block Explorer URL"}, "viewOnEtherscan": {"message": "View $1 on Etherscan", "description": "$1 is the action type. e.g (Account, Transaction, Swap)"}, "viewOnExplorer": {"message": "View on explorer"}, "viewOnOpensea": {"message": "View on Opensea"}, "viewSolanaAccount": {"message": "View Solana account"}, "viewTransaction": {"message": "View transaction"}, "viewinExplorer": {"message": "View $1 in explorer", "description": "$1 is the action type. e.g (Account, Transaction, Swap)"}, "visitSite": {"message": "Visit site"}, "visitSupportDataConsentModalAccept": {"message": "Confirm"}, "visitSupportDataConsentModalDescription": {"message": "Do you want to share your NeoNix Identifier and app version with our Support Center? This can help us better solve your problem, but is optional."}, "visitSupportDataConsentModalReject": {"message": "Don’t share"}, "visitSupportDataConsentModalTitle": {"message": "Share device details with support"}, "visitWebSite": {"message": "Visit our website"}, "wallet": {"message": "Wallet"}, "walletConnectionGuide": {"message": "our hardware wallet connection guide"}, "walletReadyLearn": {"message": "$1 you can keep this phrase safe so you never lose access to your money.", "description": "$1 is the link to Learn how"}, "walletReadyLearnRemind": {"message": "You can back up your wallets or see your Secret Recovery Phrase in Settings > Security & Password."}, "walletReadyLoseSrp": {"message": "If you lose your Secret Recovery Phrase, you won’t be able to use your wallet."}, "walletReadyLoseSrpRemind": {"message": "If you don’t back up your Secret Recovery Phrase, you’ll lose access to your funds if you get locked out of the app or get a new device."}, "wantToAddThisNetwork": {"message": "Want to add this network?"}, "wantsToAddThisAsset": {"message": "This allows the following asset to be added to your wallet."}, "warning": {"message": "Warning"}, "warningFromSnap": {"message": "Warning from $1", "description": "$1 represents the name of the snap"}, "watchEthereumAccountsDescription": {"message": "Turning this option on will give you the ability to watch Ethereum accounts via a public address or ENS name. For feedback on this Beta feature please complete this $1.", "description": "$1 is the link to a product feedback form"}, "watchEthereumAccountsToggle": {"message": "Watch Ethereum Accounts (Beta)"}, "watchOutMessage": {"message": "Beware of $1.", "description": "$1 is a link with text that is provided by the 'securityMessageLinkForNetworks' key"}, "weak": {"message": "Weak"}, "web3": {"message": "Web3"}, "web3ShimUsageNotification": {"message": "We noticed that the current website tried to use the removed window.web3 API. If the site appears to be broken, please click $1 for more information.", "description": "$1 is a clickable link."}, "webhid": {"message": "WebHID", "description": "Refers to a interface for connecting external devices to the browser. Used for connecting ledger to the browser. Read more here https://developer.mozilla.org/en-US/docs/Web/API/WebHID_API"}, "websites": {"message": "websites", "description": "Used in the 'permission_rpc' message."}, "welcomeBack": {"message": "Welcome back"}, "welcomeDescription": {"message": "Trusted by millions, NeoNix Wallet is a secure wallet making the world of web3 accessible to all."}, "welcomeGetStarted": {"message": "Get started"}, "welcomeTitle": {"message": "Welcome to NeoNix"}, "welcomeToNeoNix": {"message": "Let's get started"}, "whatsThis": {"message": "What's this?"}, "willApproveAmountForBridging": {"message": "This will approve $1 for bridging."}, "willApproveAmountForBridgingHardware": {"message": "You’ll need to confirm two transactions on your hardware wallet."}, "withdrawing": {"message": "Withdrawing"}, "wrongNetworkName": {"message": "According to our records, the network name may not correctly match this chain ID."}, "yes": {"message": "Yes"}, "you": {"message": "You"}, "youDeclinedTheTransaction": {"message": "You declined the transaction."}, "youNeedToAllowCameraAccess": {"message": "You need to allow camera access to use this feature."}, "youReceived": {"message": "You received", "description": "Label indicating the amount and asset the user received."}, "youSent": {"message": "You sent", "description": "Label indicating the amount and asset the user sent."}, "yourAccounts": {"message": "Your accounts"}, "yourActivity": {"message": "Your activity"}, "yourBalance": {"message": "Your balance"}, "yourNFTmayBeAtRisk": {"message": "Your NFT may be at risk"}, "yourNetworks": {"message": "Your networks"}, "yourPrivateSeedPhrase": {"message": "Your Secret Recovery Phrase"}, "yourTransactionConfirmed": {"message": "Transaction already confirmed"}, "yourTransactionJustConfirmed": {"message": "We weren't able to cancel your transaction before it was confirmed on the blockchain."}, "yourWalletIsReady": {"message": "Your wallet is ready!"}, "yourWalletIsReadyRemind": {"message": "We’ll remind you later"}}