{"content_security_policy": {"extension_pages": "script-src 'self' 'wasm-unsafe-eval'; object-src 'none'; frame-ancestors 'none'; font-src 'self';", "sandbox": "sandbox allow-scripts; script-src 'self' 'unsafe-inline' 'unsafe-eval'; object-src 'none'; default-src 'none'; connect-src *; font-src 'self';"}, "externally_connectable": {"matches": ["http://*/*", "https://*/*"], "ids": ["*"]}, "minimum_chrome_version": "115"}