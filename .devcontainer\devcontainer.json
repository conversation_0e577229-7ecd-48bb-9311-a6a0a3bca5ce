{
  "customizations": {
    "vscode": {
      "extensions": [
        "eamodio.gitlens",
        "github.codespaces",
        "github.copilot",
        "github.copilot-chat",
        "ms-azuretools.vscode-docker",
        "rvest.vs-code-prettier-eslint",
        "streetsidesoftware.code-spell-checker",
        "DigitalBrainstem.javascript-ejs-support"
      ],
      "settings": {
        "editor.formatOnSave": true,
        "git.ignoreRebaseWarning": true,
        "git.rebaseWhenSync": true,
        "gitlens.showWelcomeOnInstall": false
      },
      "tasks": [
        {
          "label": "Open noVNC new tab",
          "type": "shell",
          "command": "xdg-open https://$CODESPACE_NAME-6080.$GITHUB_CODESPACES_PORT_FORWARDING_DOMAIN",
          "problemMatcher": []
        }
      ]
    }
  },

  "forwardPorts": [5901, 6080],

  "image": "mcr.microsoft.com/devcontainers/universal:2-linux",

  "name": "MM Extension Codespace",

  "otherPortsAttributes": { "onAutoForward": "ignore" },

  "portsAttributes": {
    "5901": {
      "label": "local VNC",
      "onAutoForward": "ignore"
    },
    "6080": {
      "label": "noVNC web",
      "onAutoForward": "openPreview"
    }
  },

  "postAttachCommand": "/usr/local/share/desktop-init.sh && git pull; yarn download-builds",

  // This is a working Infura key, but it's on the Free Plan and has very limited requests per second
  // If you want to use your own INFURA_PROJECT_ID, follow the instructions in README.md
  "postCreateCommand": "if [ -z \"$INFURA_PROJECT_ID\" ]; then echo 'INFURA_PROJECT_ID=********************************' > .metamaskrc; fi",

  "runArgs": ["--shm-size=1g"],

  "updateContentCommand": "sudo .devcontainer/install.sh && corepack enable && COREPACK_ENABLE_DOWNLOAD_PROMPT=0 yarn --immutable && yarn tsx .devcontainer/setup-browsers.ts && echo 'export DISPLAY=:1' >> ~/.bashrc"
}
