name: Check template and add labels

on:
  issues:
    types: [opened, edited]
  pull_request_target:
    types: [opened, edited]

jobs:
  check-template-and-add-labels:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout and setup environment
        uses: MetaMask/action-checkout-and-setup@v1
        with:
          is-high-risk-environment: false
          skip-allow-scripts: true
          yarn-custom-url: ${{ vars.YARN_URL }}

      - name: Check template and add labels
        id: check-template-and-add-labels
        env:
          LABEL_TOKEN: ${{ secrets.LABEL_TOKEN }}
        run: npm run check-template-and-add-labels
