name: Identify Codeowners

on:
  pull_request:
    types:
      - opened
      - reopened
      - synchronize
      - ready_for_review

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ !(contains(github.ref, 'refs/heads/main') || contains(github.ref, 'refs/heads/master')) }}

jobs:
  identify-codeowners:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout and setup environment
        uses: MetaMask/action-checkout-and-setup@v1
        with:
          is-high-risk-environment: false
          skip-allow-scripts: true
          yarn-custom-url: ${{ vars.YARN_URL }}

      - name: Identify codeowners
        if: ${{ env.PR_COMMENT_TOKEN }}
        env:
          PR_COMMENT_TOKEN: ${{ secrets.PR_COMMENT_TOKEN }}
        run: yarn tsx ./.github/scripts/identify-codeowners.ts
