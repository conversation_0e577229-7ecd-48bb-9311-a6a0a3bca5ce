{"ignorePaths": ["app/images", "package.json"], "ignoreWords": ["acitores", "autofetch", "azuretools", "Brainstem", "BUNDLESIZE", "C01LUJL3T98", "C05QXJA7NP8", "cids", "eamodio", "initialisation", "koa<PERSON>an", "mockttp", "multibase", "multicodec", "namelookup", "pluggable", "protobufjs", "regadas", "remotedev", "ritave", "rvest", "sesify", "siginsights", "superstruct", "testrpc", "txinsights", "unzipper", "webextension", "xvfb", "zstd"], "useGitignore": true, "version": "0.2", "words": ["bignumber", "blockaid", "browserify", "browserlistrc", "cimg", "codecov", "codespace", "codespaces", "corepack", "crossorigin", "datetime", "datetimes", "dedupe", "defi", "depcheck", "devcontainer", "devcontainers", "endregion", "ensdomains", "FONTCONFIG", "foundryup", "hardfork", "hexstring", "Interactable", "ipfs", "jazzicon", "keccak", "lavadome", "lavamoat", "lavapack", "Linea", "lockdown", "metamaskbot", "metamaskrc", "metametrics", "Minipass", "mocharc", "MULTICHAIN", "MULTIPROVIDER", "noopener", "<PERSON><PERSON><PERSON><PERSON>", "npmcli", "onboarded", "pageload", "petnames", "pipefail", "quickstart", "recompiles", "retryable", "shellcheck", "SIWE", "solana", "sourcemaps", "Sourcify", "sprintf", "stablecoins", "testcase", "TESTFILES", "testid", "tsbuildinfo", "tsconfigs", "typecheck", "yargs", "yarnpkg"]}