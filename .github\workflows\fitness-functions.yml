name: Fitness Functions CI

on:
  pull_request:
    types:
      - opened
      - reopened
      - synchronize

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout and setup environment
        uses: MetaMask/action-checkout-and-setup@v1
        with:
          is-high-risk-environment: false
          fetch-depth: 0 # This is needed to checkout all branches
          skip-allow-scripts: true
          yarn-custom-url: ${{ vars.YARN_URL }}

      - name: Run fitness functions
        env:
          BASE_REF: ${{ github.event.pull_request.base.ref }}
        run: |
          # The following command generates a diff of changes between the common
          # ancestor of $BASE_REF and HEAD, and the current commit (HEAD), for
          # files in the current directory and its subdirectories. The output is
          # then saved to a file called "diff".
          git diff "$(git merge-base "origin/$BASE_REF" HEAD)" HEAD -- . > ./diff
          npm run fitness-functions -- "ci" "./diff"
