Welcome to NeoNix Wallet Codespaces!

## Quickstart Instructions

1. A "Simple Browser" will open inside the browser with noVNC -- click Connect
   - Optional steps:
     - Click the button at the upper-right of the Simple Browser tab to open the noVNC window in its own tab
     - Open the noVNC sidebar on the left, click the gear icon, change the Scaling Mode to Remote Resizing
2. Wait about 20 extra seconds on the first launch, for the scripts to finish
3. Right-click on the noVNC desktop to launch Chrome or Firefox with NeoNix Wallet pre-installed
4. Change some code, then run `yarn start` to build in dev mode
5. After a minute or two, it will finish building, and you can see your changes in the noVNC desktop

## Tips to keep your Codespaces usage lower

- You are billed for both time spent running, and for storage used
- Codespaces pause after 30 minutes of inactivity, and auto-delete after 30 days of inactivity
- You can manage your Codespaces here: https://github.com/codespaces
  - You may want to manually pause them before the 30 minute timeout
  - If you have several idle Codespaces hanging around for several days, you can quickly run out of storage quota.
    You should delete the ones you do not plan to use anymore, and probably keep only 1 or 2 in the long-term.
    It's also possible to re-use old Codespaces and switch the branch, instead of creating new ones and deleting the old ones.